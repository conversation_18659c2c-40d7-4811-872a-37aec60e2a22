import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";
const API_URL = "https://api-web-app.kamelride.com";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  server: {
    proxy: {
      '/api': API_URL, // Match your backend port
    },
  },
})
