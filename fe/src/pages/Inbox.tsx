import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { getStoredUser, removeUser, clearAllAuthState } from '../lib/auth';
import { getUserConversations } from '../lib/api';
import { Mail, MessageCircle } from 'lucide-react';
import { Header } from '../components/Header';
import { Footer } from '../components/Footer';
import { ThemeProvider } from '../components/ThemeProvider';
import { RegisterModal } from '../components/RegisterModal';
import { AuthModal } from '../components/AuthModal';
import type { User } from '../types';

export function Inbox({ region }: { region: string }) {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [conversations, setConversations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);

  useEffect(() => {
    setUser(getStoredUser());
  }, []);

  useEffect(() => {
    const fetchConversations = async () => {
      setLoading(true);
      setError(null);
      try {
        const data = await getUserConversations();
        setConversations(data);
      } catch (err: any) {
        setError(err.message || 'Failed to load conversations');
      } finally {
        setLoading(false);
      }
    };
    if (user) fetchConversations();
  }, [user]);

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <Mail className="w-12 h-12 text-desert-primary mb-4" />
        <p className="text-lg">Please sign in to view your inbox.</p>
      </div>
    );
  }

  const handleSignIn = (newUser?: User) => {
    if (newUser) {
      setUser(newUser);
    } else {
      const updatedUser = getStoredUser();
      setUser(updatedUser);
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setUser(null);
    navigate(`/`);
  };

  return (
    <ThemeProvider>
      <Header
        user={user}
        region={region}
        onSignInClick={() => setIsAuthModalOpen(true)}
        onSignOutClick={handleSignOut}
      />
      <div className="max-w-2xl mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6 flex items-center gap-2">
          <MessageCircle className="w-7 h-7 text-desert-primary" /> Inbox
        </h1>
        <div className="bg-white rounded-lg shadow divide-y">
          {loading ? (
            <div className="p-8 text-center text-desert-muted">Loading conversations...</div>
          ) : error ? (
            <div className="p-8 text-center text-red-500">{error}</div>
          ) : conversations.length === 0 ? (
            <div className="p-8 text-center text-desert-muted">No conversations yet.</div>
          ) : (
            conversations.map((conv) => (
              <div
                key={conv.id}
                className="flex items-center gap-4 px-6 py-4 cursor-pointer hover:bg-desert-light/40 transition"
                onClick={() => navigate(`/conversation-messages/${conv.user.id}`)}
              >
                <div className="w-12 h-12 rounded-full bg-desert-primary flex items-center justify-center text-white text-xl font-bold">
                  {conv.user.avatar ? (
                    <img src={conv.user.avatar} alt={conv.user.name} className="w-full h-full rounded-full object-cover" />
                  ) : (
                    conv.user.name[0]
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-desert-dark truncate">{conv.user.name}</span>
                    <span className="text-xs text-desert-muted ml-2 whitespace-nowrap">
                      {typeof conv.lastMessageTime === 'string'
                        ? new Date(conv.lastMessageTime).toLocaleString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' })
                        : conv.lastMessageTime}
                    </span>
                  </div>
                  <div className="flex justify-between items-center mt-1">
                    <span className="text-sm text-desert-muted truncate max-w-xs">{conv.lastMessage}</span>
                    {conv.unreadCount > 0 && (
                      <span className="ml-2 bg-desert-primary text-white text-xs rounded-full px-2 py-0.5">{conv.unreadCount}</span>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
      <Footer region={region} />
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        onSignIn={handleSignIn}
        onSwitchToRegister={() => {
          setIsAuthModalOpen(false);
          setIsRegisterModalOpen(true);
        }}
      />

      {/* Sign Up */}
      <RegisterModal
        isOpen={isRegisterModalOpen}
        onClose={() => setIsRegisterModalOpen(false)}
        onSignIn={handleSignIn}
      />
    </ThemeProvider>
  );
}
