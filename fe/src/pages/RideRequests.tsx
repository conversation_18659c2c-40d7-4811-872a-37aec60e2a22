import { useState, useEffect } from "react";
import { Header } from "../components/Header";
import { SearchFilters } from "../components/SearchSection";
import { ActiveRideRequests } from "../components/ActiveRideRequests";
import { ThemeProvider } from "../components/ThemeProvider";
import { AuthModal } from "../components/AuthModal";
import { RegisterModal } from "../components/RegisterModal";
import { RequestRideModal } from "../components/RequestRideModal";
import { getStoredUser, removeUser, clearAllAuthState } from "../lib/auth";
import { RideRequestSearch } from "../components/RideRequestSearch";
import { Footer } from "../components/Footer";
import type { User } from "../types";
import { StripeStatusContext } from ".././context/StripeStatusContext";
import { useNavigate } from "react-router-dom";

type RideRequestsProps = {
  region: string;
};

export function RideRequests({ region }: RideRequestsProps) {
  const [searchFilters, setSearchFilters] = useState<SearchFilters>({
    from: "",
    to: "",
    date: "",
    colleges: []
  });
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isRequestRideModalOpen, setIsRequestRideModalOpen] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean } | undefined>(undefined);

  const navigate = useNavigate();

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);

  useEffect(() => {
    const storedUser = getStoredUser(region);
    if (storedUser) {
      setUser(storedUser);
    }
  }, [region]);

  const handleSignIn = (newUser?: User) => {
    if (newUser) {
      setUser(newUser);
    } else {
      const updatedUser = getStoredUser();
      setUser(updatedUser);
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setUser(null);
    navigate(`/`);
  };

  useEffect(() => {
    const handleOpenAuthModal = () => {
      setIsAuthModalOpen(true);
    };

    window.addEventListener('open-auth-modal', handleOpenAuthModal);

    return () => {
      window.removeEventListener('open-auth-modal', handleOpenAuthModal);
    };
  }, []);

  return (
    <StripeStatusContext.Provider value={stripeStatus}>
      <ThemeProvider>
        <div className="min-h-screen bg-desert-light dark:text-desert-light pb-20 relative">
          <Header
            user={user}
            region={region}
            onSignInClick={() => setIsAuthModalOpen(true)}
            onSignOutClick={handleSignOut}
          />
          <main>
            <RideRequestSearch onSearch={setSearchFilters} region={region} />
            <ActiveRideRequests filters={searchFilters} isSignedIn={!!user} region={region} />
            {/* <PastRides isSignedIn={!!user} /> */}
          </main>

            <Footer region={region} />

            <AuthModal
              isOpen={isAuthModalOpen}
              onClose={() => setIsAuthModalOpen(false)}
              onSignIn={handleSignIn}
              onSwitchToRegister={() => {
                setIsAuthModalOpen(false);
                setIsRegisterModalOpen(true);
              }}
            />

            <RegisterModal
              isOpen={isRegisterModalOpen}
              onClose={() => setIsRegisterModalOpen(false)}
              onSignIn={handleSignIn}
            />

          <RequestRideModal
            isOpen={isRequestRideModalOpen}
            onClose={() => setIsRequestRideModalOpen(false)}
          />
        </div>
      </ThemeProvider>
    </StripeStatusContext.Provider>
  );
}
