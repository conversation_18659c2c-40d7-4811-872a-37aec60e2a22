import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Header } from "../components/Header";
import { getStoredUser, removeUser, clearAllAuthState, storeUser } from '../lib/auth';
import { Footer } from '../components/Footer';
import { ThemeProvider } from '../components/ThemeProvider';
import { AuthModal } from "../components/AuthModal";
import { RegisterModal } from "../components/RegisterModal";
import { getUserProfile, getUserRides, getUserBookings, getReviewsByReviewee } from "../lib/api";
import { Loader } from "../components/Loader";
import type { User } from "../types";
import { MessageCircle, PawPrint, Music, Cigarette, Upload, X } from "lucide-react";
import { StripeStatusContext } from ".././context/StripeStatusContext";
import ReviewModal from "../components/ReviewModal";
import { AllReviewsModal } from "../components/AllReviewsModal";
import ReviewCard from '../components/ReviewCard';
import { updateUserProfile, uploadProfileImage } from "../lib/api";
import { getApiClient } from '../lib/api/client';

const STRIPE_ENABLED = import.meta.env.VITE_STRIPE_ENABLED === 'true';


type ProfileProps = {
  region: string;
};

type ViewProfileProps = {
  region: string;
};

type Reviewer = {
  id: string;
  name: string;
  avatar: string;
};

type Ride = {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
};

type Review = {
  id: string;
  rideId: string;
  bookingId: string;
  reviewerId: string;
  revieweeId: string;
  stars: number;
  comment: string | null;
  reviewType: 'driver' | 'passenger';
  createdAt: string;
  updatedAt: string;
  reviewer: Reviewer;
  ride: Ride;
};

export function Profile({ region }: ViewProfileProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
    ;  
  const [user, setUser] = useState<User | null>(null);
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [stats, setStats] = useState({
    reviewCount: 0,
    avgRating: 0,
    ridesAsDriver: 0,
    ridesAsPassenger: 0,
  });
  const [loadingStats, setLoadingStats] = useState(true);

  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean; error?: string } | undefined>(undefined);
  const [loadingStripe, setLoadingStripe] = useState(false);
  const [stripeError, setStripeError] = useState("");

  const [reviewsAsDriver, setReviewsAsDriver] = useState<any[]>([]);
  const [reviewsAsPassenger, setReviewsAsPassenger] = useState<any[]>([]);

  const [passengerReviewDates, setPassengerReviewDates] = useState<Date[]>([]);
  const [driverReviewDates, setDriverReviewDates] = useState<Date[]>([]); 
  const [reviewsIsOpen, setReviewsIsOpen] = useState<boolean>(false);
  const [reviewTypeInView, setReviewInView] = useState<string>('driver');
  const [allReviewTypeInView, setAllReviewInView] = useState<string>('driver');

  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationForm, setVerificationForm] = useState({
    licenseFront: null as File | null,
    licenseBack: null as File | null,
    insurance: null as File | null,
    licenseExpiration: "",
    insuranceExpiration: "",
    error: "",
    isLoading: false,
  });

  const handleVerificationFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      setVerificationForm(prev => ({
        ...prev,
        [name]: files[0]
      }));
    }
  };

  const handleVerificationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setVerificationForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVerificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setVerificationForm(prev => ({ ...prev, isLoading: true, error: "" }));

    const formData = new FormData();
    if (verificationForm.licenseFront) formData.append("licenseFront", verificationForm.licenseFront);
    if (verificationForm.licenseBack) formData.append("licenseBack", verificationForm.licenseBack);
    if (verificationForm.insurance) formData.append("insurance", verificationForm.insurance);
    formData.append("licenseExpiration", verificationForm.licenseExpiration);
    formData.append("insuranceExpiration", verificationForm.insuranceExpiration);

    try {
      const apiClient = getApiClient();
      const res = await apiClient.post(
        "/api/users/upload-verification-docs",
        formData,
        { headers: { "Content-Type": "multipart/form-data" } }
      );
      if (res.status === 200) {
        setShowVerificationModal(false);
        setUser(prev => prev ? { ...prev, verificationStatus: 'pending' } : prev);
        setProfileUser(prev => prev ? { ...prev, verificationStatus: 'pending' } : prev);
      } else {
        setVerificationForm(prev => ({
          ...prev,
          error: res.data?.message || "Failed to upload documents",
          isLoading: false,
        }));
      }
    } catch (err) {
      setVerificationForm(prev => ({
        ...prev,
        error: "Failed to upload documents",
        isLoading: false,
      }));
    }
  };

  const [formData, setFormData] = useState({
    name: "",
    email: "",
    avatar: "",
    bio: "",
    preferences: {
      okayWithChatting: true,
      okayWithAnimals: false,
      okayWithMusic: true,
      okayWithSmoking: false,
    },
  });
 
  useEffect(() => {
    const storedUser = getStoredUser();
    if (!storedUser) {
      navigate(`/`);
      return;
    }
    setUser(storedUser);
    setFormData({
      name: storedUser.name,
      email: storedUser.email,
      avatar: storedUser.avatar || "",
      bio: storedUser.bio || "",
      preferences: storedUser.preferences || {
        okayWithChatting: true,
        okayWithAnimals: false,
        okayWithMusic: true,
        okayWithSmoking: false,
      },
    });
  }, [region, navigate]);

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);

  useEffect(() => {
    const storedUser = getStoredUser();
    setCurrentUser(storedUser);
    // If viewing own profile, redirect to edit profile page
    // if (storedUser && user?.id === storedUser.id) {
    //   navigate(`/profile`);
    //   return;
    // }
    // If not signed in, don't try to fetch profile
    if (!storedUser) {
      setIsLoading(false);
      return;
    }
    // Fetch the profile of the user we're viewing
    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        const userData = await getUserProfile(user?.id as string);
        setProfileUser(userData);
      } catch (err: any) {
        setError(err.response?.data?.message || "Failed to load profile");
      } finally {
        setIsLoading(false);
      }
    };

    // Fetch stats for the user
    const fetchStats = async () => {
      setLoadingStats(true);
      try {
        // Fetch reviews where revieweeId = user?.id

        const reviews = await getReviewsByReviewee(user?.id!);
        const reviewCount = reviews.length;

        const avgRating = reviewCount > 0 ? (reviews.reduce((sum: number, r: any) => sum + (r.stars || 0), 0) / reviewCount) : 0;
        // Fetch rides as driver
        const rides = await getUserRides(user?.id!);
        const ridesAsDriver = rides.length;
        // Fetch bookings as passenger
        const bookings = await getUserBookings(user?.id!);
        const ridesAsPassenger = bookings.length;
        //console.log({ "rides it?": [bookings] });
        console.log({ reviews })
        setStats({ reviewCount, avgRating, ridesAsDriver, ridesAsPassenger });
      } catch (err) {
        setStats({ reviewCount: 0, avgRating: 0, ridesAsDriver: 0, ridesAsPassenger: 0 });
      } finally {
        setLoadingStats(false);
      }
    };
    if (typeof user?.id === 'string' && user?.id) {
      fetchProfile();
      fetchStats();
    }
  }, [user?.id, region, navigate]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handlePreferenceChange = (preference: string) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: !prev.preferences[preference as keyof typeof prev.preferences]
      }
    }));
  };

  const handleImageUpload = async (file: File) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image size must be less than 5MB');
      return;
    }

    setIsUploadingImage(true);
    setError("");

    try {
      const result = await uploadProfileImage(file);
      setFormData(prev => ({
        ...prev,
        avatar: result.avatar
      }));
      setUser(result.user);
      storeUser(result.user); // Store the updated user data
      setImagePreview(null);
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to upload image");
    } finally {
      setIsUploadingImage(false);
    }
    console.log({ "profile-user": user });
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);

      // Upload image
      handleImageUpload(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      console.log({ "formData": formData });
      const updatedUser = await updateUserProfile(formData);
      setUser(updatedUser);
      storeUser(updatedUser); // Store the updated user data
      setIsEditing(false);
      setImagePreview(null);
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to update profile");
    } finally {
      /* No matter what happens, set the submit button back to saying 'Submit' after 
      the handleSubmit code goes through. */
      setIsLoading(false);
    }
  };

  const handleSignIn = (newUser?: User) => {
    if (newUser) {
      setCurrentUser(newUser);
    } else {
      const storedUser = getStoredUser();
      setCurrentUser(storedUser);
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setCurrentUser(null);
    navigate(`/`);
  };

  const handleConnectBank = async () => {
    setLoadingStripe(true);
    setStripeError("");
    try {
      const res = await fetch(`${import.meta.env.VITE_API_URL}/api/stripe/connect`, {
        method: "POST",
        headers: { Authorization: `Bearer ${user?.token}` }
      });
      const data = await res.json();
      if (data.onboardingUrl) {
        window.location.href = data.onboardingUrl;
      } else {
        setStripeError(data.error || "Could not get onboarding link.");
      }
    } catch (e) {
      setStripeError("Could not start Stripe onboarding.");
    }
    setLoadingStripe(false);
  };  

  const renderPreferenceIcon = (preference: string, value: boolean) => {
    switch (preference) {
      case 'okayWithChatting':
        return <MessageCircle size={20} className={value ? "text-blue-500" : "text-red-500"} />;
      case 'okayWithAnimals':
        return <PawPrint size={20} className={value ? "text-orange-500" : "text-red-500"} />;
      case 'okayWithMusic':
        return <Music size={20} className={value ? "text-purple-500" : "text-red-500"} />;
      case 'okayWithSmoking':
        return <Cigarette size={20} className={value ? "text-gray-500" : "text-red-500"} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    async function fetchAllReviews() {
      if (!user?.id) return; // wait until user is defined
      try {
        const response = await getReviewsByReviewee(user?.id!);

        response.forEach((reviewElement) => {
          const date: Date = new Date(reviewElement.createdAt);

          if (reviewElement.reviewType === "driver") {
            setReviewsAsDriver(prev => [...prev, reviewElement]);
            setDriverReviewDates(prev => [...prev, date]);
          } else {
            setReviewsAsPassenger(prev => [...prev, reviewElement]);
            setPassengerReviewDates(prev => [...prev, date]);
          }
        });
      } catch (error) {
        console.error(error);
      }
    }

    fetchAllReviews();
  }, [user?.id]);


  const getPreferenceLabel = (preference: string, value: boolean) => {
    switch (preference) {
      case 'okayWithChatting':
        return value ? 'Okay with chatting' : 'NOT okay with chatting';
      case 'okayWithAnimals':
        return value ? 'Okay with animals' : 'NOT okay with animals';
      case 'okayWithMusic':
        return value ? 'Okay with music' : 'NOT okay with music';
      case 'okayWithSmoking':
        return value ? 'Okay with smoking' : 'NOT okay with smoking';
      default:
        return preference;
    }
  };

  return (
    <StripeStatusContext.Provider value={stripeStatus}>
      <ThemeProvider>
        <div className="min-h-screen bg-desert-light dark:text-desert-light pb-20 relative">
          <Header
            user={currentUser}
            region={region}
            onSignInClick={() => setIsAuthModalOpen(true)}
            onSignOutClick={handleSignOut}
          />

          {!currentUser ? (
            <div className="text-center text-xl text-desert-muted py-8 md:py-12">
              Please sign in to view user profiles.
            </div>
          ) : isLoading ? (
            <div className="flex justify-center items-center py-16">
              <Loader size="lg" />
            </div>
          ) : error ? (
            <div className="text-center text-xl text-red-500 py-8 md:py-12">
              {error}
            </div>
          ) : profileUser ? (
            <div className="py-8 md:py-12 max-w-2xl mx-auto px-4">
              <div className="text-center">
                {isEditing ? (
                    <form onSubmit={handleSubmit} className="space-y-4">
                        {error && <div className="text-red-500 text-center">{error}</div>}

                        <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold">Edit Profile</h2>
                        </div>

                        {/*Note that having the value attribute be set to the corresponding
                    key within formData enables the input fields to be altered whenever
                    the text input changes. Furthermore, the the text fields are already
                    pre-set with the formData info. */}
                        <div>
                        <label className="block text-sm font-medium mb-1">Name</label>
                        <input
                            type="text"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-700"
                            required
                        />
                        </div>

                        <div>
                        <label className="block text-sm font-medium mb-1">Email</label>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-700"
                            required
                        />
                        </div>

                        {/* Profile Image Upload */}
                        <div>
                        <label className="block text-sm font-medium mb-1">Profile Picture</label>

                        {/* Current Image Display */}
                        {(formData.avatar || imagePreview) && (
                            <div className="mb-4 text-center">
                            <div className="relative inline-block">
                                <img
                                src={imagePreview || formData.avatar}
                                alt="Profile preview"
                                className="rounded-full w-24 h-24 mx-auto object-cover border-2 border-gray-300"
                                />
                                {imagePreview && (
                                <button
                                    type="button"
                                    onClick={handleRemoveImage}
                                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                                >
                                    <X size={16} />
                                </button>
                                )}
                            </div>
                            </div>
                        )}

                        {/* Upload Button */}
                        <div className="flex items-center justify-center w-full">
                            <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                            <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                <Upload className="w-8 h-8 mb-4 text-gray-500 dark:text-gray-400" />
                                <p className="mb-2 text-sm text-gray-500 dark:text-gray-400">
                                <span className="font-semibold">Click to upload</span> or drag and drop
                                </p>
                                <p className="text-xs text-gray-500 dark:text-gray-400">PNG, JPG, GIF up to 5MB</p>
                            </div>
                            <input
                                ref={fileInputRef}
                                type="file"
                                className="hidden"
                                accept="image/*"
                                onChange={handleFileSelect}
                                disabled={isUploadingImage}
                            />
                            </label>
                        </div>

                        {isUploadingImage && (
                            <div className="text-center mt-2">
                            <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-desert-primary"></div>
                            <span className="ml-2 text-sm text-gray-500">Uploading...</span>
                            </div>
                        )}
                        </div>

                        {/* Bio Input */}
                        <div>
                        <label className="block text-sm font-medium mb-1">Bio</label>
                        <textarea
                            name="bio"
                            value={formData.bio}
                            onChange={handleInputChange}
                            rows={3}
                            className="w-full p-2 border rounded dark:bg-gray-800 dark:border-gray-700"
                            placeholder="Tell us about yourself..."
                        />
                        </div>

                        {/* Ride Preferences */}
                        <div>
                        <label className="block text-sm font-medium mb-3">Ride Preferences</label>
                        <div className="space-y-3">
                            {Object.entries(formData.preferences).map(([key, value]) => (
                            <label key={key} className="flex items-center space-x-3 cursor-pointer">
                                <input
                                type="checkbox"
                                checked={value}
                                onChange={() => handlePreferenceChange(key)}
                                className="w-4 h-4 text-desert-primary cursor-pointer"
                                />
                                <div className="flex items-center space-x-2">
                                {renderPreferenceIcon(key, value)}
                                <span className="text-sm">{getPreferenceLabel(key, value)}</span>
                                </div>
                            </label>
                            ))}
                        </div>
                        </div>

                        {/* Back to displaying profile information. */}
                        <div className="flex justify-end space-x-2 pt-4">
                        <button
                            type="button"
                            onClick={() => {
                            setIsEditing(false);
                            setImagePreview(null);
                            }}
                            className="px-4 py-2 border rounded"
                        >
                            Cancel
                        </button>
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="px-4 py-2 bg-desert-primary text-white rounded"
                        >
                            {isLoading ? "Saving..." : "Save Changes"}
                        </button>
                        </div>
                    </form>
                ) : (
                <div>
                    <div className="text-center">
                    
                   <div className="relative inline-block w-24 h-24 mx-auto mb-4">
                      {profileUser.avatar ? (
                        <img
                          src={profileUser.avatar}
                          alt={`${profileUser.name}'s profile`}
                          className={`rounded-full w-24 h-24 object-cover ${profileUser.driverVerified ? 'ring-4 ring-desert-primary' : ''}`}
                        />
                      ) : (
                        <div className={`rounded-full w-24 h-24 bg-desert-primary flex items-center justify-center text-white text-2xl ${profileUser.driverVerified ? 'ring-4 ring-desert-primary' : ''}`}>
                          {profileUser.name[0]}
                        </div>
                      )}
                      {profileUser.driverVerified && (
                        <span className="absolute bottom-0 right-0 bg-desert-primary rounded-full p-1 flex items-center justify-center shadow-lg border-2 border-white">
                          <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                            <path d="M7.629 15.314a1 1 0 0 1-1.414 0l-3.243-3.243a1 1 0 1 1 1.414-1.414l2.536 2.536 6.364-6.364a1 1 0 1 1 1.414 1.414l-7.071 7.071z"/>
                          </svg>
                        </span>
                      )}
                    </div>

                    <h1 className="text-3xl font-bold mb-2 flex items-center justify-center gap-2">
                      {profileUser.name}
                      {profileUser?.verificationStatus === 'pending' && (
                        <span className="text-yellow-600 text-base font-semibold">(Pending driver verification)</span>
                      )}
                      {profileUser?.verificationStatus === 'approved' && (
                        <span className="text-green-600 text-base font-semibold">(Verified Driver)</span>
                      )}
                      {profileUser?.verificationStatus === 'rejected' && (
                        <span className="text-red-600 text-base font-semibold">(Driver verification rejected)</span>
                      )}
                    </h1>
                    <p className="text-desert-muted mb-6">{profileUser.email}</p>
                    {/* User stats */}
                    <div className="flex flex-wrap justify-center gap-4 mb-4">
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                        <div className="text-xs text-gray-500">Reviews</div>
                        <div className="font-bold text-lg">{loadingStats ? '...' : stats.reviewCount}</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                        <div className="text-xs text-gray-500">Avg. Rating</div>
                        <div className="flex items-center justify-center gap-1">
                          {loadingStats ? '...' : (
                            <span className="font-bold text-lg">{stats.avgRating.toFixed(2)}</span>
                          )}
                          {/* Star display */}
                          <span className="text-yellow-400">{[...Array(5)].map((_, i) => (
                            <svg key={i} className={`inline w-4 h-4 ${i < Math.round(stats.avgRating) ? 'fill-yellow-400' : 'fill-gray-300 dark:fill-gray-600'}`} viewBox="0 0 20 20"><polygon points="10,1 12.59,6.99 19,7.64 14,12.26 15.18,18.51 10,15.27 4.82,18.51 6,12.26 1,7.64 7.41,6.99" /></svg>
                          ))}</span>
                        </div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                        <div className="text-xs text-gray-500">Rides as Driver</div>
                        <div className="font-bold text-lg">{loadingStats ? '...' : stats.ridesAsDriver}</div>
                      </div>
                      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                        <div className="text-xs text-gray-500">Rides as Passenger</div>
                        <div className="font-bold text-lg">{loadingStats ? '...' : stats.ridesAsPassenger}</div>
                      </div>
                    </div>



                    {profileUser.bio && (
                      <div className="mb-6 text-left">
                        <h3 className="text-lg font-semibold mb-2">About</h3>
                        <p className="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                          {profileUser.bio}
                        </p>
                      </div>
                    )}

                    {(reviewsAsDriver.length > 0 || reviewsAsPassenger.length > 0) && (
                      <div className = "grid grid-cols-2 w-48 h-12 mx-auto mb-8 text-white">
                        <button onClick = {() => setReviewInView('driver')} className = {reviewTypeInView === 'driver'? "border rounded-tl-xl rounded-bl-xl bg-desert-primary text-sm" : "border rounded-tl-xl rounded-bl-xl bg-gray-500 text-sm"}>Driver</button>
                        <button onClick = {() => setReviewInView('passenger')} className = {reviewTypeInView === 'passenger'? "border rounded-tr-xl rounded-br-xl bg-desert-primary text-sm" : " rounded-tr-xl rounded-br-xl bg-gray-500 border text-sm"}>Passenger</button>
                      </div>
                    )}

                    {/* Real Reviews on Profile */}
                    <div className="grid grid-cols-2 row-span-2 gap-5 text-left items-start">
                      {reviewsAsDriver.length > 0 && reviewTypeInView === 'driver' && (
                        reviewsAsDriver.slice(0, 2).map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={driverReviewDates ? driverReviewDates[index] : new Date()}
                          />
                        ))
                      )}

                      {reviewsAsPassenger.length > 0 && reviewTypeInView === 'passenger' && (
                        reviewsAsPassenger.slice(0, 2).map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={passengerReviewDates ? passengerReviewDates[index] : new Date()}
                          />
                        ))
                      )}
                    </div>

                    {(reviewsAsPassenger?.length ?? 0) > 2 || (reviewsAsDriver?.length?? 0) > 2 ?(
                      <button 
                      className = "mt-5 mx-auto flex items-center gap-2 bg-desert-primary text-white px-4 py-2 rounded-md hover:bg-desert-primary/90 transition-colors"
                      onClick={() => setReviewsIsOpen(true)}>
                        See all reviews
                      </button>
                    ): null
                    }

                    <AllReviewsModal
                      isOpen = {reviewsIsOpen}
                      onClose = {() => {setReviewsIsOpen(false)}}
                    >

                      <div className = "grid grid-cols-2 w-48 h-12 mx-auto mb-8 text-white">
                          <button onClick = {() => setAllReviewInView('driver')} className = {allReviewTypeInView === 'driver'? "border rounded-tl-xl rounded-bl-xl bg-desert-primary text-sm" : "border rounded-tl-xl rounded-bl-xl bg-gray-500 text-sm"}>Driver</button>
                          <button onClick = {() => setAllReviewInView('passenger')} className = {allReviewTypeInView === 'passenger'? "border rounded-tr-xl rounded-br-xl bg-desert-primary text-sm" : " rounded-tr-xl rounded-br-xl bg-gray-500 border text-sm"}>Passenger</button>
                      </div>

                    <div className="grid grid-cols-1 gap-5 text-left items-start">
                      {allReviewTypeInView === 'driver' && (
                        reviewsAsDriver.map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={driverReviewDates ? driverReviewDates[index] : new Date()}
                          />
                        ))
                      )}

                      {allReviewTypeInView === 'passenger' && (
                        reviewsAsPassenger.map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={passengerReviewDates ? passengerReviewDates[index] : new Date()}
                          />
                        ))
                      )}
                    </div>
                    </AllReviewsModal>

                    {profileUser.preferences && (
                      <div className="mb-6 text-left">
                        <h3 className="text-lg font-semibold mb-3 mt-8 text-center">Ride Preferences</h3>
                        <div className="grid grid-cols-2 gap-3">
                          {Object.entries(profileUser.preferences).map(([key, value]) => (
                            <div key={key} className={`flex items-center space-x-2 p-2 rounded-lg`}>
                              {renderPreferenceIcon(key, value)}
                              <span className={`text-sm ${value ? 'text-green-700 dark:text-green-300' : 'text-red-500 dark:text-red-400'}`}>
                                {getPreferenceLabel(key, value)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    <div className="text-center mt-8">
                        <div className="flex justify-center gap-4">
                          <button
                            onClick={() => setIsEditing(true)}
                            className="px-4 py-2 bg-desert-primary text-white rounded"
                          >
                            Edit Profile
                          </button>
                          {(profileUser?.verificationStatus === 'none' || profileUser?.verificationStatus === 'rejected') && (
                            <button
                              onClick={() => setShowVerificationModal(true)}
                              className="px-4 py-2 bg-green-600 text-white rounded"
                            >
                              Become a Verified Driver
                            </button>
                          )}
                        </div>
                        {STRIPE_ENABLED && (
                        <div className="mt-4">
                            {/* Show connect button if no account or payouts not enabled */}
                            {(!stripeStatus || 
                            stripeStatus?.error === 'No Stripe account found.' ||
                            (stripeStatus && !stripeStatus.payouts_enabled) ||
                            stripeError) && (
                            <button
                                onClick={handleConnectBank}
                                disabled={loadingStripe}
                                className="px-4 py-2 bg-desert-primary text-white rounded"
                            >
                                {loadingStripe ? "Loading..." : "Connect Bank Account via Stripe"}
                            </button>
                            )}
                            
                            {/* Show connected button if payouts are enabled */}
                            {stripeStatus && stripeStatus.payouts_enabled && (
                            <button
                                type="button"
                                onClick={async () => {
                                const res = await fetch("/api/stripe/login-link", {
                                    method: "POST",
                                    headers: { Authorization: `Bearer ${user?.token}` }
                                });
                                const data = await res.json();
                                if (data.url) {
                                    window.open(data.url, "_blank");
                                } else {
                                    alert("Could not open Stripe dashboard.");
                                }
                                }}
                                className="px-4 py-2 bg-desert-primary text-white rounded font-normal mt-2 transition hover:bg-desert-primary/90 inline-flex items-center"
                                title="View or manage your Stripe payout details"
                            >
                                Bank Account Connected
                                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                                </svg>
                            </button>
                            )}
                        </div>
                        )}

                        {/* Verification Modal */}
                        {showVerificationModal && (
                          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
                            <div className="bg-white dark:bg-gray-800 rounded-lg p-8 w-full max-w-md shadow-lg relative">
                              <button
                                className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
                                onClick={() => setShowVerificationModal(false)}
                              >
                                <X size={20} />
                              </button>
                              <h2 className="text-2xl font-bold mb-4 text-center">Driver Verification</h2>
                              <form onSubmit={handleVerificationSubmit} className="space-y-6">
                                {verificationForm.error && (
                                  <div className="text-red-500 text-center">{verificationForm.error}</div>
                                )}
                                <div className="flex flex-col gap-6">
                                  <div>
                                    <label className="block text-sm font-medium mb-2">Driver's License (Front)</label>
                                    <label className="flex flex-col items-center justify-center px-4 py-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 transition min-h-[56px]">
                                      <Upload className="w-5 h-5 mb-2 text-desert-primary" />
                                      <span className="text-sm text-gray-700 dark:text-gray-200 text-center w-full">
                                        {verificationForm.licenseFront ? verificationForm.licenseFront.name : "Choose file"}
                                      </span>
                                      <input
                                        type="file"
                                        name="licenseFront"
                                        accept="image/*"
                                        onChange={handleVerificationFileChange}
                                        required
                                        className="hidden"
                                      />
                                    </label>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium mb-2">Driver's License (Back)</label>
                                    <label className="flex flex-col items-center justify-center px-4 py-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 transition min-h-[56px]">
                                      <Upload className="w-5 h-5 mb-2 text-desert-primary" />
                                      <span className="text-sm text-gray-700 dark:text-gray-200 text-center w-full">
                                        {verificationForm.licenseBack ? verificationForm.licenseBack.name : "Choose file"}
                                      </span>
                                      <input
                                        type="file"
                                        name="licenseBack"
                                        accept="image/*"
                                        onChange={handleVerificationFileChange}
                                        required
                                        className="hidden"
                                      />
                                    </label>
                                  </div>
                                  <div>
                                    <label className="block text-sm font-medium mb-2">Car Insurance Document</label>
                                    <label className="flex flex-col items-center justify-center px-4 py-4 bg-gray-100 dark:bg-gray-700 rounded-lg border border-gray-300 dark:border-gray-600 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 transition min-h-[56px]">
                                      <Upload className="w-5 h-5 mb-2 text-desert-primary" />
                                      <span className="text-sm text-gray-700 dark:text-gray-200 text-center w-full">
                                        {verificationForm.insurance ? verificationForm.insurance.name : "Choose file"}
                                      </span>
                                      <input
                                        type="file"
                                        name="insurance"
                                        accept="image/*"
                                        onChange={handleVerificationFileChange}
                                        required
                                        className="hidden"
                                      />
                                    </label>
                                  </div>
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                      <label className="block text-sm font-medium mb-2">License Expiration Date</label>
                                      <input
                                        type="date"
                                        name="licenseExpiration"
                                        value={verificationForm.licenseExpiration}
                                        onChange={handleVerificationInputChange}
                                        className="w-full rounded border border-gray-300 dark:border-gray-600 p-2"
                                      />
                                    </div>
                                    <div>
                                      <label className="block text-sm font-medium mb-2">Insurance Expiration Date</label>
                                      <input
                                        type="date"
                                        name="insuranceExpiration"
                                        value={verificationForm.insuranceExpiration}
                                        onChange={handleVerificationInputChange}
                                        className="w-full rounded border border-gray-300 dark:border-gray-600 p-2"
                                      />
                                    </div>
                                  </div>
                                </div>
                                <div className="flex justify-end space-x-2 pt-4">
                                  <button
                                    type="button"
                                    onClick={() => setShowVerificationModal(false)}
                                    className="px-4 py-2 border rounded"
                                  >
                                    Cancel
                                  </button>
                                  <button
                                    type="submit"
                                    disabled={verificationForm.isLoading}
                                    className="px-4 py-2 bg-green-600 text-white rounded"
                                  >
                                    {verificationForm.isLoading ? "Uploading..." : "Submit"}
                                  </button>
                                </div>
                              </form>
                            </div>
                          </div>
                        )}
                    </div>
                    </div>
                </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center text-xl text-desert-muted py-8 md:py-12">
              User not found.
            </div>
          )}

          <Footer region={region} />

          <AuthModal
            isOpen={isAuthModalOpen}
            onClose={() => setIsAuthModalOpen(false)}
            onSignIn={handleSignIn}
            onSwitchToRegister={() => {
              setIsAuthModalOpen(false);
              setIsRegisterModalOpen(true);
            }}
          />

          <RegisterModal
            isOpen={isRegisterModalOpen}
            onClose={() => setIsRegisterModalOpen(false)}
            onSignIn={handleSignIn}
          />
        </div>
      </ThemeProvider>
    </StripeStatusContext.Provider>
  );
}
