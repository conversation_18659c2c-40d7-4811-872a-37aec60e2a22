import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Header } from "../components/Header";
import { getStoredUser, removeUser, clearAllAuthState } from '../lib/auth';
import { Footer } from '../components/Footer';
import { ThemeProvider } from '../components/ThemeProvider';
import { AuthModal } from "../components/AuthModal";
import { RegisterModal } from "../components/RegisterModal";
import { getUserProfile, getUserRides, getUserBookings, getReviewsByReviewee } from "../lib/api";
import { Loader } from "../components/Loader";
import type { User } from "../types";
import { MessageCircle, PawPrint, Music, Cigarette } from "lucide-react";
import { StripeStatusContext } from ".././context/StripeStatusContext";
import ReviewModal from "../components/ReviewModal";
import { AllReviewsModal } from "../components/AllReviewsModal";
import ReviewCard from '../components/ReviewCard';

type ViewProfileProps = {
  region: string;
};

type Reviewer = {
  id: string;
  name: string;
  avatar: string;
};

type Ride = {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
};

type Review = {
  id: string;
  rideId: string;
  bookingId: string;
  reviewerId: string;
  revieweeId: string;
  stars: number;
  comment: string | null;
  reviewType: 'driver' | 'passenger';
  createdAt: string;
  updatedAt: string;
  reviewer: Reviewer;
  ride: Ride;
};

export function ViewProfile({ region }: ViewProfileProps) {
  const { userId } = useParams<{ userId: string }>();
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [profileUser, setProfileUser] = useState<User | null>(null);
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [stats, setStats] = useState({
    reviewCount: 0,
    avgRating: 0,
    ridesAsDriver: 0,
    ridesAsPassenger: 0,
  });
  const [loadingStats, setLoadingStats] = useState(true);

  const navigateToChat = (userId: string) => {
    navigate(`/conversation-messages/${userId}`);
  };
  const [user, setUser] = useState(getStoredUser());
  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean } | undefined>(undefined);

  const [reviewsAsDriver, setReviewsAsDriver] = useState<any[]>([]);
  const [reviewsAsPassenger, setReviewsAsPassenger] = useState<any[]>([]);
  const [reviewInView, setReviewInView] = useState<string>('driver');

  const [passengerReviewDates, setPassengerReviewDates] = useState<Date[]>([]);
  const [driverReviewDates, setDriverReviewDates] = useState<Date[]>([]); 
  const [reviewsIsOpen, setReviewsIsOpen] = useState<boolean>(false);

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);

  useEffect(() => {
    const storedUser = getStoredUser();
    setCurrentUser(storedUser);
    // If viewing own profile, redirect to edit profile page
    if (storedUser && userId === storedUser.id) {
      navigate(`/profile`);
      return;
    }
    // If not signed in, don't try to fetch profile
    if (!storedUser) {
      setIsLoading(false);
      return;
    }
    // Fetch the profile of the user we're viewing
    const fetchProfile = async () => {
      try {
        setIsLoading(true);
        const userData = await getUserProfile(userId as string);
        setProfileUser(userData);
      } catch (err: any) {
        setError(err.response?.data?.message || "Failed to load profile");
      } finally {
        setIsLoading(false);
      }
    };
    // Fetch stats for the user
    const fetchStats = async () => {
      setLoadingStats(true);
      try {
        // Fetch reviews where revieweeId = userId

        const reviews = await getReviewsByReviewee(userId!);
        const reviewCount = reviews.length;

        const avgRating = reviewCount > 0 ? (reviews.reduce((sum: number, r: any) => sum + (r.stars || 0), 0) / reviewCount) : 0;
        // Fetch rides as driver
        const rides = await getUserRides(userId!);
        const ridesAsDriver = rides.length;
        // Fetch bookings as passenger
        const bookings = await getUserBookings(userId!);
        const ridesAsPassenger = bookings.length;
        //console.log({ "rides it?": [bookings] });
        console.log({ reviews })
        setStats({ reviewCount, avgRating, ridesAsDriver, ridesAsPassenger });
      } catch (err) {
        setStats({ reviewCount: 0, avgRating: 0, ridesAsDriver: 0, ridesAsPassenger: 0 });
      } finally {
        setLoadingStats(false);
      }
    };
    if (typeof userId === 'string' && userId) {
      fetchProfile();
      fetchStats();
    }
  }, [userId, region, navigate]);

  const handleSignIn = (newUser?: User) => {
    if (newUser) {
      setCurrentUser(newUser);
    } else {
      const storedUser = getStoredUser();
      setCurrentUser(storedUser);
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setCurrentUser(null);
    navigate(`/`);
  };

  const renderPreferenceIcon = (preference: string, value: boolean) => {
    switch (preference) {
      case 'okayWithChatting':
        return <MessageCircle size={20} className={value ? "text-blue-500" : "text-red-500"} />;
      case 'okayWithAnimals':
        return <PawPrint size={20} className={value ? "text-orange-500" : "text-red-500"} />;
      case 'okayWithMusic':
        return <Music size={20} className={value ? "text-purple-500" : "text-red-500"} />;
      case 'okayWithSmoking':
        return <Cigarette size={20} className={value ? "text-gray-500" : "text-red-500"} />;
      default:
        return null;
    }
  };

  useEffect(() => {
    async function fetchAllReviews() {
      try{
        const response = await getReviewsByReviewee(userId as string);
        
        response.map((reviewElement) => {
          const date: Date = new Date(reviewElement.createdAt); 

          if (reviewElement.reviewType === 'driver'){
            setReviewsAsDriver([...reviewsAsDriver, reviewElement])
            setDriverReviewDates([...driverReviewDates, date])
          }
          else{
            setReviewsAsPassenger([...reviewsAsPassenger, reviewElement])
            setPassengerReviewDates([...passengerReviewDates, date])
          }
        })
      }

      catch(error){
        console.log(error)
      }
    }

    fetchAllReviews()
  }, [])

  const getPreferenceLabel = (preference: string, value: boolean) => {
    switch (preference) {
      case 'okayWithChatting':
        return value ? 'Okay with chatting' : 'NOT okay with chatting';
      case 'okayWithAnimals':
        return value ? 'Okay with animals' : 'NOT okay with animals';
      case 'okayWithMusic':
        return value ? 'Okay with music' : 'NOT okay with music';
      case 'okayWithSmoking':
        return value ? 'Okay with smoking' : 'NOT okay with smoking';
      default:
        return preference;
    }
  };

  return (
    <StripeStatusContext.Provider value={stripeStatus}>
      <ThemeProvider>
        <div className="min-h-screen bg-desert-light dark:text-desert-light pb-20 relative">
          <Header
            user={currentUser}
            region={region}
            onSignInClick={() => setIsAuthModalOpen(true)}
            onSignOutClick={handleSignOut}
          />

          {!currentUser ? (
            <div className="text-center text-xl text-desert-muted py-8 md:py-12">
              Please sign in to view user profiles.
            </div>
          ) : isLoading ? (
            <div className="flex justify-center items-center py-16">
              <Loader size="lg" />
            </div>
          ) : error ? (
            <div className="text-center text-xl text-red-500 py-8 md:py-12">
              {error}
            </div>
          ) : profileUser ? (
            <div className="py-8 md:py-12 max-w-2xl mx-auto px-4">
              <div className="text-center">
                <div className="relative inline-block w-24 h-24 mx-auto mb-4">
                  {profileUser.avatar ? (
                    <img
                      src={profileUser.avatar}
                      alt={`${profileUser.name}'s profile`}
                      className={`rounded-full w-24 h-24 object-cover ${profileUser.driverVerified ? 'ring-4 ring-green-500' : ''}`}
                    />
                  ) : (
                    <div className={`rounded-full w-24 h-24 bg-desert-primary flex items-center justify-center text-white text-2xl ${profileUser.driverVerified ? 'ring-4 ring-green-500' : ''}`}>
                      {profileUser.name[0]}
                    </div>
                  )}
                  {profileUser.driverVerified && (
                    <span className="absolute bottom-2 right-2 bg-green-500 rounded-full p-1 flex items-center justify-center">
                      {/* Checkmark SVG */}
                      <svg width="20" height="20" fill="white" viewBox="0 0 20 20">
                        <path d="M7.629 15.314a1 1 0 0 1-1.414 0l-3.243-3.243a1 1 0 1 1 1.414-1.414l2.536 2.536 6.364-6.364a1 1 0 1 1 1.414 1.414l-7.071 7.071z"/>
                      </svg>
                    </span>
                  )}
                </div>
                <h1 className="text-3xl font-bold mb-2">{profileUser.name}</h1>
                {/* User stats */}
                <div className="flex flex-wrap justify-center gap-4 mb-4">
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                    <div className="text-xs text-gray-500">Reviews</div>
                    <div className="font-bold text-lg">{loadingStats ? '...' : stats.reviewCount}</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                    <div className="text-xs text-gray-500">Avg. Rating</div>
                    <div className="flex items-center justify-center gap-1">
                      {loadingStats ? '...' : (
                        <span className="font-bold text-lg">{stats.avgRating.toFixed(2)}</span>
                      )}
                      {/* Star display */}
                      <span className="text-yellow-400">{[...Array(5)].map((_, i) => (
                        <svg key={i} className={`inline w-4 h-4 ${i < Math.round(stats.avgRating) ? 'fill-yellow-400' : 'fill-gray-300 dark:fill-gray-600'}`} viewBox="0 0 20 20"><polygon points="10,1 12.59,6.99 19,7.64 14,12.26 15.18,18.51 10,15.27 4.82,18.51 6,12.26 1,7.64 7.41,6.99" /></svg>
                      ))}</span>
                    </div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                    <div className="text-xs text-gray-500">Rides as Driver</div>
                    <div className="font-bold text-lg">{loadingStats ? '...' : stats.ridesAsDriver}</div>
                  </div>
                  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg px-4 py-2 text-center min-w-[110px]">
                    <div className="text-xs text-gray-500">Rides as Passenger</div>
                    <div className="font-bold text-lg">{loadingStats ? '...' : stats.ridesAsPassenger}</div>
                  </div>
                </div>



                {profileUser.bio && (
                  <div className="mb-6 text-left">
                    <h3 className="text-lg font-semibold mb-2">About</h3>
                    <p className="text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                      {profileUser.bio}
                    </p>
                  </div>
                )}

                {(reviewsAsDriver.length > 0 || reviewsAsPassenger.length > 0) && (
                  <div className = "grid grid-cols-2 w-48 h-12 mx-auto mb-8 text-white">
                    <button onClick = {() => setReviewInView('driver')} className = {reviewInView === 'driver'? "border rounded-tl-xl rounded-bl-xl bg-desert-primary text-sm" : "border rounded-tl-xl rounded-bl-xl bg-gray-500 text-sm"}>Driver</button>
                    <button onClick = {() => setReviewInView('passenger')} className = {reviewInView === 'passenger'? "border rounded-tr-xl rounded-br-xl bg-desert-primary text-sm" : " rounded-tr-xl rounded-br-xl bg-gray-500 border text-sm"}>Passenger</button>
                  </div>
                )}

                {/* Real Reviews on Profile */}
                <div className="grid grid-cols-2 row-span-2 gap-5 text-left items-start">
                  {reviewsAsDriver.length > 0 && reviewInView === 'driver' && (
                    reviewsAsDriver.slice(0, 2).map((review, index) => (
                      <ReviewCard
                        key={index}
                        review={review}
                        reviewDate={driverReviewDates ? driverReviewDates[index] : new Date()}
                      />
                    ))
                  )}

                  {reviewsAsPassenger.length > 0 && reviewInView === 'passenger' && (
                    reviewsAsPassenger.slice(0, 2).map((review, index) => (
                      <ReviewCard
                        key={index}
                        review={review}
                        reviewDate={passengerReviewDates ? passengerReviewDates[index] : new Date()}
                      />
                    ))
                  )}
                </div>
                
                {(reviewsAsPassenger?.length ?? 0) > 2 || (reviewsAsDriver?.length?? 0) > 2 ?(
                  <button 
                  className = "mt-5 mx-auto flex items-center gap-2 bg-desert-primary text-white px-4 py-2 rounded-md hover:bg-desert-primary/90 transition-colors"
                  onClick={() => setReviewsIsOpen(true)}>
                    See all reviews
                  </button>
                ): null
                }


                

                <AllReviewsModal
                  isOpen = {reviewsIsOpen}
                  onClose = {() => {setReviewsIsOpen(false)}}
                >
                  <div className="grid grid-cols-2 row-span-2 gap-5 text-left items-start">
                    {reviewsAsDriver.length > 0 && (
                      <div className="grid grid-cols-1 gap-5 text-left">
                        <p className="text-gray-500">Reviews as Driver</p>
                        {reviewsAsDriver.map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={driverReviewDates ? driverReviewDates[index] : new Date()}
                          />
                        ))}
                      </div>
                    )}

                    {reviewsAsPassenger.length > 0 && (
                      <div className="grid grid-cols-1 gap-5 text-left self-start">
                        <p className="text-gray-500">Reviews as Passenger</p>
                        {reviewsAsPassenger.map((review, index) => (
                          <ReviewCard
                            key={index}
                            review={review}
                            reviewDate={passengerReviewDates ? passengerReviewDates[index] : new Date()}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                </AllReviewsModal>

                {profileUser.preferences && (
                  <div className="mb-6 text-left">
                    <h3 className="text-lg font-semibold mb-3 mt-8 text-center">Ride Preferences</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {Object.entries(profileUser.preferences).map(([key, value]) => (
                        <div key={key} className={`flex items-center space-x-2 p-2 rounded-lg`}>
                          {renderPreferenceIcon(key, value)}
                          <span className={`text-sm ${value ? 'text-green-700 dark:text-green-300' : 'text-red-500 dark:text-red-400'}`}>
                            {getPreferenceLabel(key, value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {currentUser && profileUser && (
                  <div className="mt-4 mx-auto">
                    <button
                      onClick={() => navigateToChat(profileUser.id)}
                      className="mx-auto flex items-center gap-2 bg-desert-primary text-white px-4 py-2 rounded-md hover:bg-desert-primary/90 transition-colors"
                    >
                      <MessageCircle size={18} />
                      <span>Message {profileUser.name}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center text-xl text-desert-muted py-8 md:py-12">
              User not found.
            </div>
          )}

          <Footer region={region} />

          <AuthModal
            isOpen={isAuthModalOpen}
            onClose={() => setIsAuthModalOpen(false)}
            onSignIn={handleSignIn}
            onSwitchToRegister={() => {
              setIsAuthModalOpen(false);
              setIsRegisterModalOpen(true);
            }}
          />

          <RegisterModal
            isOpen={isRegisterModalOpen}
            onClose={() => setIsRegisterModalOpen(false)}
            onSignIn={handleSignIn}
          />
        </div>
      </ThemeProvider>
    </StripeStatusContext.Provider>
  );
}
