
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Users, Briefcase } from "lucide-react";

import apiClient from '../lib/api/client';
import {
  getUserRides,
  getUserBookings,
  getUserRideRequests,
  deleteRideRequest,
  getRideMessages,
  getRideBookings,
  deleteRidePost,
  updateBookingStatus
} from '../lib/api';
import { clearAllAuthState, getStoredUser } from '../lib/auth';
import { Ride, RideRequest } from '../types';
import { Loader } from '../components/Loader';
import { Header } from '../components/Header';
import { ThemeProvider } from '../components/ThemeProvider';
import { Footer } from '../components/Footer';
import { Trash2 } from 'lucide-react';
import { RideDetailsModal } from '../components/RideDetailsModal';
import { StripeStatusContext } from ".././context/StripeStatusContext";
import { convertUTCToLocal } from '../utils/convertUTCToLocal';

type MyRidesPageProps = {
  region: string; // Keep for UI customization
};

export function MyRidesPage({ region }: MyRidesPageProps) {
  const [postedRides, setPostedRides] = useState<Ride[]>([]);
  const [bookedRides, setBookedRides] = useState<any[]>([]);
  const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('posted');
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState(getStoredUser());
  // New state for selected ride
  const [selectedRide, setSelectedRide] = useState<Ride | null>(null);
  const navigate = useNavigate();
  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean } | undefined>(undefined);

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);

  useEffect(() => {
    if (!user) {
      navigate(`/`);
      return;
    }

    fetchUserRides();
  }, [user, navigate, region]);

  const fetchUserRides = async () => {
    if (!user?.token) return;

    setIsLoading(true);
    setError(null);
    try {
      const [rides, bookings, requests] = await Promise.all([
        getUserRides(user.id),
        getUserBookings(user.id),
        getUserRideRequests()
      ]);

      // For posted rides, fetch additional data (messages and passengers)
      const enhancedRides = await Promise.all((rides || []).map(async (ride) => {
        try {
          // Fetch messages and passengers in parallel
          const [messages, passengers] = await Promise.all([
            getRideMessages(ride.id),
            getRideBookings(ride.id)
          ]);

          return {
            ...ride,
            driver: ride.driver || { id: user.id, name: user.name },
            messages: messages || [],
            passengers: passengers || []
          };
        } catch (error) {
          console.error(`Failed to fetch details for ride ${ride.id}:`, error);
          return ride;
        }
      }));

      // For booked rides, ensure we have the ride details and messages
      const enhancedBookings = await Promise.all((bookings || []).map(async (booking) => {
        if (!booking.ride?.id) return booking;

        try {
          // Fetch messages for this ride
          const messages = await getRideMessages(booking.ride.id);

          return {
            ...booking,
            ride: {
              ...booking.ride,
              messages: messages || []
            }
          };
        } catch (error) {
          console.error(`Failed to fetch messages for booked ride ${booking.ride.id}:`, error);
          return booking;
        }
      }));

      setPostedRides(enhancedRides || []);
      setBookedRides(enhancedBookings || []);
      setRideRequests(requests || []);
    } catch (error) {
      console.error('Failed to fetch user rides:', error);
      setError('Failed to load your rides. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const formatLocation = (location: string): string => {
    return location
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setUser(null);
    navigate(`/`);
  };

  const handleAccept = async (bookingId: string) => {
  const confirmed = window.confirm("Are you sure you want to accept this passenger? This action cannot be undone.");
  if (!confirmed) return;
  try {
    await updateBookingStatus(bookingId, "confirmed");
    fetchUserRides();
  } catch (error) {
    console.log("Failed to update booking status to confirmed", error);
  }
};

  const handleReject = async (bookingId: string) => {
    const confirmed = window.confirm("Are you sure you want to reject this passenger? This action cannot be undone.");
    if (!confirmed) return;
    try {
      await updateBookingStatus(bookingId, "cancelled");
      fetchUserRides();
    } catch (error) {
      console.error("Failed to update booking status to cancelled:", error);
    }
  };

  const handleCancelBooking = async (bookingId: string) => {
    if (!user?.token) return;
    if (!window.confirm('Are you sure you want to cancel this booking?')) return;
    try {
      await apiClient.delete(`/api/bookings/${bookingId}`, {
        headers: { Authorization: `Bearer ${user.token}` }
      });
      // Remove the cancelled booking from UI
      //setBookedRides(prev => prev.filter(b => b.id !== bookingId));
      // CHANGED to update the booking in the UI
      setBookedRides(prev =>
        prev.map(b =>
          b.id === bookingId ? { ...b, status: 'cancelled' } : b
        )
      );
    } catch (error) {
      alert('Failed to cancel booking.');
      console.error(error);
    }
  };

  const handleCancelRide = async (rideId: string) => {
    if (!user?.token) return;

    if (confirm("Are you sure you want to cancel this ride?")) {
      try {
        await apiClient.delete(`/api/rides/${rideId}`, {
          headers: { Authorization: `Bearer ${user.token}` }
        });
        // Update the state to remove the cancelled ride
        setPostedRides(prevRides =>
          prevRides.filter(ride => ride.id !== rideId)
        );
      } catch (error) {
        console.error('Failed to cancel ride:', error);
        setError('Failed to cancel ride. Please try again.');
      }
    }
  };

  // Handler for deleting a ride request
  const handleDeleteRequest = async (requestId: string) => {
    if (!user?.token) return;

    if (confirm("Are you sure you want to delete this ride request?")) {
      try {
        await deleteRideRequest(requestId);
        // Update the state to remove the deleted request
        setRideRequests(prevRequests =>
          prevRequests.filter(request => request.id !== requestId)
        );
      } catch (error) {
        console.error('Failed to delete ride request:', error);
        setError('Failed to delete ride request. Please try again.');
      }
    }
  };

  const handleDeletePost = async (rideId: string) => {
    if (!user?.token) return;

    if (confirm("Are you sure you want to delete this post?")) {
      try {
        await deleteRidePost(rideId);
        setPostedRides(prevPosts =>
          prevPosts.filter(ride => ride.id !== rideId));
      } catch (error) {
        console.error('Failed to delete posted ride', error);
        setError('Failed to delete posted ride. Please try again.')
      }
    }
  }

  // Add handler for viewing ride details
  const handleViewDetails = (ride: Ride) => {
    setSelectedRide(ride);
  };

  // Helper function to render notes
  const renderNotes = (notes: string[] | undefined) => {
    if (!notes || notes.length === 0) return null;

    return (
      <div className="mt-2 border-t pt-2">
        <p className="text-sm font-medium mb-1">Notes:</p>
        <ul className="list-disc list-inside text-sm text-gray-600">
          {notes.map((note, idx) => (
            <li key={idx}>{note}</li>
          ))}
        </ul>
      </div>
    );
  };

  // Helper function to render passengers preview
  const renderPassengersPreview = (passengers: any[] | undefined) => {
    if (!passengers || passengers.length === 0) {
      return <p className="text-gray-500 italic text-sm">No passengers yet</p>;
    }

    return (
      <div className="mt-2 border-t pt-2">
        <p className="text-sm font-medium mb-1">Passengers:</p>
        <div className="flex flex-wrap gap-1">
          {passengers.map((passenger) => (
            <span
              key={passenger.id}
              className={`text-xs px-2 py-1 rounded-full ${passenger.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                passenger.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}
            >
              {passenger.user?.name || 'Unknown'} ({passenger.seats} seat {passenger.seats !== 1 ? 's' : ''})
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderPassengersApproval = (passengers: any[] | undefined) => {
    if (!passengers || passengers.length === 0) {
      return <p className="text-gray-500 italic text-sm">No passengers yet</p>;
    }

    return (
      <div className="mt-2 border-t pt-2">
        <p className="text-sm font-medium mb-1">Passengers:</p>
        <div className="flex flex-wrap gap-1">
          {passengers.map((passenger) => (
            <div key={passenger.id} className="flex items-center justify-between">
              <span
                className={`text-xs px-10 py-1 rounded-full mr-4 ${passenger.status === 'confirmed'
                  ? 'bg-green-100 text-green-800'
                  : passenger.status === 'pending'
                    ? 'bg-yellow-100 text-yellow-800'
                    : 'bg-red-300 text-red-950'
                  }`}
              >
                <span className="block">{passenger.user?.name || 'Unknown'}</span>
                <span className="inline-flex items-center">
                  (
                  {passenger.seats} <Users size={12} className="mx-1" />{passenger.seats !== 1 ? 's' : ''} {passenger.suitcases} <Briefcase size={12} className='mx-1' />
                  )
                </span>
              </span>
              <div className="flex gap-2">
                {passenger.status === 'cancelled' ? (
                  <span className="text-xs font-semibold px-3 py-1 rounded bg-red-600 text-white"
                  style={{
                    backgroundColor: '#ffa3a3',
                    color: 'white',
                    borderRadius: '1.0rem'
                  }}
                  >
                    REJECTED
                  </span>
                ) : passenger.status === 'confirmed' ? (
                  <span
                    className="text-xs font-semibold px-3 py-1 rounded"
                    style={{
                      backgroundColor: '#7ed9a7',
                      color: 'white',
                      borderRadius: '1.0rem'
                    }}
                  >
                    ACCEPTED
                  </span>
                ) : (
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleAccept(passenger.id)}
                      className="text-green-600 text-xs font-semibold px-2 py-1 border border-green-600 rounded hover:bg-green-600 hover:text-white transition"
                    >
                      Accept
                    </button>
                    <button
                      onClick={() => handleReject(passenger.id)}
                      className="text-red-600 text-xs font-semibold px-2 py-1 border border-red-600 rounded hover:bg-red-600 hover:text-white transition"
                    >
                      Reject
                    </button>
                  </div>
                )}
              </div>
            </div>

          ))}
        </div>
      </div>
    );
  };

  // Helper function to render messages preview
  const renderMessagesPreview = (messages: any[] | undefined) => {
    if (!messages || messages.length === 0) {
      return <p className="text-gray-500 italic text-sm">No messages yet</p>;
    }

    const latestMessages = messages.slice(-2); // Show last 2 messages

    return (
      <div className="mt-2 border-t pt-2">
        <p className="text-sm font-medium mb-1">Recent Messages:</p>
        {latestMessages.map((msg, idx) => (
          <div key={idx} className="text-sm mb-1">
            <span className="font-medium">{msg.userName || msg.user?.name || 'Unknown'}: </span>
            <span className="text-gray-600">{msg.content.length > 30 ? `${msg.content.substring(0, 30)}...` : msg.content}</span>
          </div>
        ))}
        {messages.length > 2 && (
          <p className="text-xs text-gray-500">+{messages.length - 2} more messages</p>
        )}
      </div>
    );
  };

  return (
    <StripeStatusContext.Provider value={stripeStatus}>
    <ThemeProvider>
      <div className={`min-h-screen bg-${region === 'nj' ? 'nj' : 'desert'}-background dark:bg-${region === 'nj' ? 'nj' : 'desert'}-background-dark dark:text-${region === 'nj' ? 'nj' : 'desert'}-text-dark relative`}>
        <Header
          user={user}
          region={region}
          onSignInClick={() => navigate(`/`)}
          onSignOutClick={handleSignOut}
        />

        <main className="container mx-auto px-4 py-8 pb-20">
          <h1 className="text-3xl font-bold mb-6">My Rides</h1>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader size="lg" />
              <p className="mt-4 text-gray-600 dark:text-gray-300">Loading your rides...</p>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <div className="flex border-b mb-6">
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'posted'
                    ? 'text-desert-primary border-b-2 border-desert-primary'
                    : 'text-gray-500'}`}
                  onClick={() => setActiveTab('posted')}
                >
                  Rides I Posted
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'booked'
                    ? 'text-desert-primary border-b-2 border-desert-primary'
                    : 'text-gray-500'}`}
                  onClick={() => setActiveTab('booked')}
                >
                  Rides I Booked
                </button>
                <button
                  className={`px-4 py-2 font-medium ${activeTab === 'requests'
                    ? 'text-desert-primary border-b-2 border-desert-primary'
                    : 'text-gray-500'}`}
                  onClick={() => setActiveTab('requests')}
                >
                  My Ride Requests
                </button>
              </div>
              {error && (
                <div className="text-red-500 text-center my-4">
                  {error}
                </div>
              )}
              {activeTab === 'posted' ? (
                postedRides.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {postedRides.map(ride => (
                      <div key={ride.id} className="bg-desert-light dark:bg-gray-700 rounded-lg p-4 shadow">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-lg">{formatLocation(ride.from)} → {formatLocation(ride.to)}</h3>
                          <span className="bg-desert-primary text-white px-2 py-1 rounded text-sm">${ride.price}/seat</span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          <p className="mb-1">Date: {convertUTCToLocal(ride.date, ride.time).localDate}</p>
                          <p className="mb-1">Time: {convertUTCToLocal(ride.date, ride.time).localTime}</p>
                          <p className="mb-1">Seats: {ride.seatsAvailable}/{ride.totalSeats}</p>
                          <p>Suitcases: {ride.suitcasesAvailable}/{ride.totalSuitcases}</p>
                        </div>
                        {/* Show notes if available */}
                        {renderNotes(ride.notes)}
                        {/* Show passengers preview */}
                        {renderPassengersPreview(ride.passengers)}
                        {/* Show messages preview */}
                        {renderMessagesPreview(ride.messages)}
                        <div className="flex gap-2 mt-3">
                          <button
                            onClick={() => setSelectedRide(ride)}
                            className="flex-1 bg-desert-primary text-white px-3 py-1.5 rounded-md text-sm hover:bg-desert-accent transition-colors"
                          >
                            View Details
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center py-8 text-gray-500">You haven't posted any rides yet.</p>
                )
              ) : activeTab === 'booked' ? (
                bookedRides.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {bookedRides.map(booking => (
                      <div key={booking.id} className="bg-desert-light dark:bg-gray-700 rounded-lg p-4 shadow">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-lg">
                            {booking.ride && booking.ride.from ?
                              `${formatLocation(booking.ride.from)} → ${formatLocation(booking.ride.to)}` :
                              'Ride details unavailable'}
                          </h3>
                          <span className={`px-2 py-1 rounded text-sm ${booking.status === 'confirmed' ? 'bg-green-500 text-white' :
                            booking.status === 'pending' ? 'bg-yellow-500 text-white' :
                              'bg-red-500 text-white'
                            }`}>
                            {booking.status}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          {booking.ride ? (
                            <>
                              <p className="mb-1">
                                Date: {booking.ride.date && booking.ride.time
                                  ? convertUTCToLocal(booking.ride.date, booking.ride.time).localDate
                                  : 'N/A'}
                              </p>
                              <p className="mb-1">
                                Time: {booking.ride.date && booking.ride.time
                                  ? convertUTCToLocal(booking.ride.date, booking.ride.time).localTime
                                  : 'N/A'}
                              </p>
                              <p className="mb-1">Seats booked: {booking.seats}</p>
                              <p>Suitcases: {booking.suitcases || 0}</p>
                              {/* Show driver info */}
                              {booking.ride.driver && (
                                <div className="mt-2 border-t pt-2">
                                  <p className="text-sm font-medium">Driver: {booking.ride.driver.name}</p>
                                </div>
                              )}
                              {/* Show notes if available */}
                              {renderNotes(booking.ride.notes)}
                              {/* Show messages preview */}
                              {renderMessagesPreview(booking.ride.messages)}
                            </>
                          ) : (
                            <p>Ride details unavailable</p>
                          )}
                        </div>
                        {booking.ride && (
                          <button
                            onClick={() => handleViewDetails(booking.ride)}
                            className="mt-3 w-full bg-desert-primary text-white px-3 py-1.5 rounded-md text-sm hover:bg-desert-accent transition-colors"
                          >
                            View Details
                          </button>
                        )}
                        {(booking.status === 'confirmed' || booking.status === 'pending') && (
                          <button
                            onClick={() => handleCancelBooking(booking.id)}
                            className="mt-2 w-full bg-red-500 text-white px-3 py-1.5 rounded-md text-sm hover:bg-red-700 transition-colors"
                          >
                            Cancel Booking
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center py-8 text-gray-500">You haven't booked any rides yet.</p>
                )
              ) : activeTab === 'requests' ? (
                rideRequests.length > 0 ? (
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {rideRequests.map(request => (
                      <div key={request.id} className="bg-desert-light dark:bg-gray-700 rounded-lg p-4 shadow">
                        <div className="flex justify-between items-start mb-3">
                          <h3 className="font-semibold text-lg">{formatLocation(request.from)} → {formatLocation(request.to)}</h3>
                          <button
                            onClick={() => handleDeleteRequest(request.id)}
                            className="text-red-500 hover:text-red-700 p-1"
                            title="Delete request"
                          >
                            <Trash2 size={18} />
                          </button>
                        </div>
                        <div className="text-sm text-gray-600 dark:text-gray-300">
                          <p className="mb-1">
                            Date: {request.date && request.time
                              ? convertUTCToLocal(request.date, request.time).localDate
                              : 'N/A'}
                          </p>
                          <p className="mb-1">
                            Time: {request.date && request.time
                              ? convertUTCToLocal(request.date, request.time).localTime
                              : 'N/A'}
                          </p>
                          <p className="mb-1">Seats needed: {request.seatsNeeded}</p>
                          <p>Suitcases: {request.suitcasesNeeded}</p>
                          {/* Show notes if available */}
                          {request.notes && request.notes.length > 0 && (
                            <div className="mt-2 border-t pt-2">
                              <p className="font-medium">Notes:</p>
                              <ul className="list-disc list-inside">
                                {request.notes.map((note, index) => (
                                  <li key={index}>{note}</li>
                                ))}
                              </ul>
                            </div>
                          )}
                          {/* Show posted time if available */}
                          {request.postedTime && (
                            <p className="mt-2 text-xs text-gray-500">Posted: {request.postedTime}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-center py-8 text-gray-500">You haven't requested any rides yet.</p>
                )
              ) : null}
            </div>
          )}
          </main>

          <Footer region={region} />

          {/* Add RideDetailsModal component */}
          {selectedRide && (
            <RideDetailsModal
              isOpen={true}
              onClose={() => setSelectedRide(null)}
              ride={selectedRide}
              userId={user?.id}
              userName={user?.name}
              token={user?.token || null}
              bookings={bookedRides}
              setRides={setPostedRides}
              setUserBookings={setBookedRides}
              region={region}
              apiUrl={import.meta.env.VITE_API_URL || "http://localhost:5000"}
            />
          )}
        </div>
      </ThemeProvider>
    </StripeStatusContext.Provider>
  );
}
