import { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Header } from "../components/Header";
import { Footer } from "../components/Footer";
import { ThemeProvider } from "../components/ThemeProvider";
import { getStoredUser, removeUser, clearAllAuthState } from "../lib/auth";
import { AuthModal } from "../components/AuthModal";
import { RegisterModal } from "../components/RegisterModal";
import { getUserProfile, sendConversationMessage, getConversation } from "../lib/api";
import { Send, ArrowLeft } from "lucide-react";
import type { User, ConversationMessage } from "../types";
import { StripeStatusContext } from ".././context/StripeStatusContext";

export function ConversationMessages({ region }: { region: string }) {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [otherUser, setOtherUser] = useState<User | null>(null);
  const [conversationMessages, setConversationMessages] = useState<ConversationMessage[]>([]);
  const [newConversationMessage, setNewConversationMessage] = useState("");

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [isRegisterModalOpen, setIsRegisterModalOpen] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [user, setUser] = useState(getStoredUser());
  const [stripeStatus, setStripeStatus] = useState<{ payouts_enabled: boolean } | undefined>(undefined);

  useEffect(() => {
    async function fetchStripeStatus() {
      if (user && user.token) {
        try {
          const res = await fetch('/api/stripe/account-status', {
            headers: { Authorization: `Bearer ${user.token}` }
          });
          if (res.ok) {
            const data = await res.json();
            setStripeStatus({ payouts_enabled: data.payouts_enabled });
          } else {
            setStripeStatus(undefined);
          }
        } catch (error) {
          setStripeStatus(undefined);
        }
      } else {
        setStripeStatus(undefined);
      }
    }
    fetchStripeStatus();
  }, [user]);
  
  // Load current user from storage
  useEffect(() => {
    const user = getStoredUser();
    if (!user) {
      navigate(`/login?redirect=conversation-messages/${userId}`);
      return;
    }
    setCurrentUser(user);

    // Prevent messaging yourself
    if (user.id === userId) {
      navigate(`/profile`);
      return;
    }
  }, [region, userId, navigate]);

  // Load other user and conversation
  useEffect(() => {
    const loadData = async () => {
      if (!currentUser) return;

      try {
        setLoading(true);

        // Get other user details
        const user = await getUserProfile(userId as string);
        setOtherUser(user);

        // Get conversation history
        const conversation = await getConversation(userId as string);
        setConversationMessages(conversation);

        setLoading(false);
      } catch (err: any) {
        setError(err.message || "Failed to load conversation");
        setLoading(false);
      }
    };

    loadData();
  }, [currentUser, userId]);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [conversationMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newConversationMessage.trim() || !currentUser || !otherUser) return;

    try {
      const sentMessage = await sendConversationMessage(otherUser.id, newConversationMessage);
      setConversationMessages([...conversationMessages, sentMessage]);
      setNewConversationMessage("");
    } catch (err: any) {
      setError(err.message || "Failed to send message");
    }
  };

  const handleSignIn = (newUser?: User) => {
    if (newUser) {
      setCurrentUser(newUser);
    } else {
      const storedUser = getStoredUser();
      setCurrentUser(storedUser);
    }
  };

  const handleSignOut = () => {
    clearAllAuthState();
    setCurrentUser(null);
    navigate(`/`);
  };

  return (
    <StripeStatusContext.Provider value={stripeStatus}>
      <ThemeProvider>
        <div className="flex flex-col min-h-screen">
          <Header
            user={currentUser}
            region={region}
            onSignInClick={() => setIsAuthModalOpen(true)}
            onSignOutClick={handleSignOut}
          />

          <main className="flex-grow container mx-auto px-4 py-6">
            {/* Back button */}
            <button
              onClick={() => navigate(`/profile/${userId}`)}
              className="flex items-center gap-2 text-desert-primary mb-4"
            >
              <ArrowLeft size={18} />
              <span>Back to Profile</span>
            </button>

            {loading ? (
              <div className="flex justify-center items-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-desert-primary"></div>
              </div>
            ) : error ? (
              <div className="text-red-500 text-center">{error}</div>
            ) : otherUser ? (
              <div className="flex flex-col h-[calc(100vh-250px)] bg-white rounded-lg shadow-md">
                {/* Chat header */}
                <div className="p-4 border-b flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-desert-primary flex items-center justify-center text-white">
                    {otherUser.avatar ? (
                      <img
                        src={otherUser.avatar}
                        alt={otherUser.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      otherUser.name[0]
                    )}
                  </div>
                  <div>
                    <h2 className="font-semibold">{otherUser.name}</h2>
                  </div>
                </div>

                {/* Messages container */}
                <div className="flex-grow p-4 overflow-y-auto">
                  {conversationMessages.length === 0 ? (
                    <div className="text-center text-gray-500 mt-10">
                      No messages yet. Start the conversation!
                    </div>
                  ) : (
                    conversationMessages.map((message) => (
                      <div
                        key={message.id}
                        className={`mb-4 max-w-[40%] w-fit ${message.senderId === currentUser?.id
                          ? "ml-auto bg-desert-primary text-white rounded-tl-lg rounded-tr-lg rounded-bl-lg"
                          : "mr-auto bg-gray-100 rounded-tl-lg rounded-tr-lg rounded-br-lg"
                          } p-3 rounded-lg`}
                      >
                        <p>{message.content}</p>
                        <p className={`text-xs mt-1 ${message.senderId === currentUser?.id ? "text-desert-light" : "text-gray-500"
                          }`}>
                          {new Date(message.createdAt).toLocaleTimeString([], {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </p>
                      </div>
                    ))
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Message input */}
                <form onSubmit={handleSendMessage} className="p-4 border-t flex gap-2">
                  <input
                    type="text"
                    value={newConversationMessage}
                    onChange={(e) => setNewConversationMessage(e.target.value)}
                    placeholder="Type a message..."
                    className="flex-grow p-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-desert-primary"
                  />
                  <button
                    type="submit"
                    disabled={!newConversationMessage.trim()}
                    className="bg-desert-primary text-white p-2 rounded-md disabled:opacity-50"
                  >
                    <Send size={20} />
                  </button>
                </form>
              </div>
            ) : null}
          </main>

          <Footer region={region} />
          <AuthModal
            isOpen={isAuthModalOpen}
            onClose={() => setIsAuthModalOpen(false)}
            onSignIn={handleSignIn}
            onSwitchToRegister={() => {
              setIsAuthModalOpen(false);
              setIsRegisterModalOpen(true);
            }}
          />

          <RegisterModal
            isOpen={isRegisterModalOpen}
            onClose={() => setIsRegisterModalOpen(false)}
            onSignIn={handleSignIn}
          />
        </div>
      </ThemeProvider>
    </StripeStatusContext.Provider>
  );
};
