import { User } from "../types";
import { auth, googleProvider } from "./firebase";
import { signInWithPopup, signOut } from "firebase/auth";
import { getApiClient } from "./api/client";

const STORAGE_KEY_PREFIX = "revy_user";

// Get current region from localStorage or use default
const getCurrentRegion = (): string => {
  // Try to get region from URL path first
  const pathMatch = window.location.pathname.match(/^\/(cornell|nj)/);
  if (pathMatch && pathMatch[1]) {
    return pathMatch[1];
  }

  // Fallback to stored region or default
  // return localStorage.getItem('current_region') || 'cornell';
  return 'cornell';
};

export function getStoredUser(): User | null {
  const region = getCurrentRegion();
  const storageKey = `${STORAGE_KEY_PREFIX}_${region}`;
  const stored = localStorage.getItem(storageKey);
  return stored ? JSON.parse(stored) : null;
}

export function storeUser(user: User) {
  const region = getCurrentRegion();
  const storageKey = `${STORAGE_KEY_PREFIX}_${region}`;
  localStorage.setItem(storageKey, JSON.stringify(user));

  // Also store token separately for API client
  if (user.token) {
    localStorage.setItem(`auth_token_${region}`, user.token);
  }
}

export function removeUser() {
  const region = getCurrentRegion();
  const storageKey = `${STORAGE_KEY_PREFIX}_${region}`;
  localStorage.removeItem(storageKey);
  localStorage.removeItem(`auth_token_${region}`);

  // Also sign out from Firebase to clear Google auth state
  signOut(auth).catch(error => {
    console.error("Error signing out from Firebase:", error);
  });
}

// New function to clear all auth state and force fresh sign-in
export function clearAllAuthState() {
  // Clear all region-specific auth data
  const regions = ['cornell', 'nj'];
  regions.forEach(region => {
    localStorage.removeItem(`${STORAGE_KEY_PREFIX}_${region}`);
    localStorage.removeItem(`auth_token_${region}`);
  });

  // Sign out from Firebase to clear Google auth state
  signOut(auth).catch(error => {
    console.error("Error signing out from Firebase:", error);
  });
}

// Login with email and password
export async function loginWithEmail(
  email: string,
  password: string
): Promise<User> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/auth/login`, {
      email,
      password,
    });

    const user = response.data;
    storeUser(user);
    return user;
  } catch (error) {
    console.error("Login failed:", error);
    throw error;
  }
}

// Register new user
export async function registerUser(
  name: string,
  email: string,
  password: string
): Promise<User> {
  try {
    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/auth/register`, {
      name,
      email,
      password,
    });

    const user = response.data;
    storeUser(user);
    return user;
  } catch (error) {
    console.error("Registration failed:", error);
    throw error;
  }
}

export async function mockGoogleSignIn(): Promise<User | null> {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    const tokenId = await result.user.getIdToken();

    const apiClient = getApiClient();
    const response = await apiClient.post(`/api/auth/google`, {
      tokenId,
    });

    const user = response.data;
    storeUser(user);
    console.log({ "mockGoogleSignIn-user": user });
    return user;
  } catch (error: any) {
    console.error("Google sign-in error:", error);

    // Check for domain restriction error
    if (error.response?.data?.code === 'INVALID_EMAIL_DOMAIN') {
      // Clear any stored auth state for this region
      const region = getCurrentRegion();
      localStorage.removeItem(`auth_token_${region}`);

      // Throw specific error to be handled by UI
      throw new Error(error.response.data.message);
    }

    return null;
  }
}

// Add this function to check if a token is about to expire
export function setupTokenExpirationCheck(interval = 60000) { // Check every minute by default
  const checkInterval = setInterval(() => {
    const region = getCurrentRegion();
    const token = localStorage.getItem(`auth_token_${region}`);

    if (!token) {
      return; // No token to check
    }

    // Make a request to verify the token
    const apiClient = getApiClient();
    apiClient.get('/api/auth/verify-token')
      .catch(error => {
        if (error.response?.status === 401) {
          // Token is invalid or expired, clear it
          localStorage.removeItem(`auth_token_${region}`);
          localStorage.removeItem(`revy_user_${region}`);

          // Log for debugging
          console.warn('Token verification failed, logging out user');

          // Dispatch the expired event
          window.dispatchEvent(new CustomEvent('auth:expired', {
            detail: { region }
          }));

          // Redirect to home page
          window.location.href = `/`;
        }
      });
  }, interval);

  return () => clearInterval(checkInterval);
}
