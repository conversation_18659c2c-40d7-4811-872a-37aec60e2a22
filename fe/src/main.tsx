import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import App from './App.tsx'
import { MyRidesPage } from './pages/MyRidesPage.tsx'
import { RideRequests } from './pages/RideRequests.tsx'
import { Profile } from './pages/Profile.tsx';
import './index.css'
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { ViewProfile } from './pages/ViewProfile.tsx';
import { ConversationMessages } from "./pages/ConversationMessages.tsx";
import { Inbox } from "./pages/Inbox";

// Stripe imports
import { loadStripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

// Region-specific route wrapper
const RegionRoutes = ({ region }: { region: string }) => {
  return (
    <Routes>
      <Route path="/" element={<App region={region} />} />
      <Route path="/my-rides" element={<MyRidesPage region={region} />} />
      <Route path="/ride-requests" element={<RideRequests region={region} />} />
      <Route path="/profile/:userId" element={<ViewProfile region={region} />} />
      <Route path="/profile" element={<Profile region={region} />} />
      <Route path="/conversation-messages/:userId" element={<ConversationMessages region={region} />} />
      <Route path="/inbox" element={<Inbox region={region} />} />
    </Routes>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <ToastContainer position="top-center" />
      <Routes>
        {/* Redirect root to NJ by default */}
        <Route path="/*" element={<RegionRoutes region="cornell" />} />
        <Route path="/nj*" element={<Navigate to="/" replace />} />

        {/* Region-specific routes */}
        <Route path="/*" element={<RegionRoutes region="cornell" />} />
        {/* <Route path="/nj/*" element={<RegionRoutes region="nj" />} /> */}

        {/* Fallback for old URLs */}
        {/* <Route path="/my-rides" element={<Navigate to="/nj/my-rides" replace />} />
        <Route path="/ride-requests" element={<Navigate to="/nj/ride-requests" replace />} />
        <Route path="/profile" element={<Navigate to="/nj/profile" replace />} /> */}
      </Routes>
    </BrowserRouter>
  </React.StrictMode>,
)