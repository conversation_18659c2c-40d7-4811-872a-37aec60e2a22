export type Ride = {
  passengers: any;
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
  seatsAvailable: number;
  totalSeats: number;
  suitcasesAvailable: number;
  totalSuitcases: number;
  pickupAddress?: string | null;
  pickupLat?: number | null;
  pickupLng?: number | null;
  dropoffAddress?: string | null;
  dropoffLat?: number | null;
  dropoffLng?: number | null;
  driver: {
    id: string;
    name: string;
    info: string;
    avatarColor: string;
    avatar: string;
  };
  price: number;
  postedTime: string;
  notes?: string[];
  messages?: Message[];
  status?: string;
  hasCompletedBookings?: boolean;
  canDelete?: boolean;
};

export type RideRequest = {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
  seatsNeeded: number;
  suitcasesNeeded: number;
  notes: string[];
  postedTime: string;
  passenger?: {
    id: string,
    name: string,
    avatarColor: string;
    avatar: string;
  }
  preferredPickupAddress?: string | null;
  preferredPickupLat?: number | null;
  preferredPickupLng?: number | null;
  preferredDropoffAddress?: string | null;
  preferredDropoffLat?: number | null;
  preferredDropoffLng?: number | null;
}

export type User = {
  id: string;
  email: string;
  name: string;
  password: string;
  avatar: string | null;
  bio: string;
  preferences: {
    okayWithChatting: boolean;
    okayWithAnimals: boolean;
    okayWithMusic: boolean;
    okayWithSmoking: boolean;
  };
  token: string;
  role?: string;
  stripeAccountId?: string | null;
  driverVerified: boolean;
  verifiedUntil: string | null;
  verificationStatus: 'none' | 'pending' | 'approved' | 'rejected';
  verificationNotes: string | null;
  licenseFrontKey: string | null;
  licenseBackKey: string | null;
  insuranceKey: string | null;
  licenseExpiration: string | null;
  insuranceExpiration: string | null;
  verificationSubmittedAt: string | null;
  verificationReviewedAt: string | null;
  createdAt?: string;
  updatedAt?: string;
};

export type Message = {
  id: string;
  userId: string | undefined;
  userName: string | undefined;
  content: string;
  timestamp: string;
};

export type ConversationMessage = {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  createdAt: string;
  read: boolean;
};

export type Conversation = {
  id: string;
  participants: string[];
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
};

declare global {
  interface Window {
    refreshRides?: () => Promise<void>;
    refreshMyRides?: () => Promise<void>;
    refreshRideRequests?: () => Promise<void>;
  }
}
