.container {
  @apply p-4 sm:p-6;
}

.header {
  @apply flex flex-col sm:flex-row justify-between items-start mb-4 sm:mb-6;
}

.title {
  @apply text-lg sm:text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.postedTime {
  @apply text-desert-muted text-xs sm:text-sm;
}

.priceContainer {
  @apply text-left sm:text-right mt-2 sm:mt-0;
}

.price {
  @apply text-lg sm:text-xl font-semibold text-desert-dark dark:text-desert-light;
}

.pricePerSeat {
  @apply text-xs sm:text-sm text-desert-muted;
}

.detailsContainer {
  @apply space-y-2 sm:space-y-3 mb-4 sm:mb-6 text-sm sm:text-base;
}

.detailItem {
  @apply flex items-center text-desert-muted;
}

.detailIcon {
  @apply w-4 h-4 mr-2 flex-shrink-0;
}

.notesSection {
  @apply border-t border-desert-muted pt-4 sm:pt-6 mb-4 sm:mb-6;
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.sectionTitle {
  @apply font-medium text-desert-dark dark:text-desert-light mb-2 sm:mb-3 text-base sm:text-lg;
}

.notesList {
  @apply space-y-2;
}

.noteItem {
  @apply bg-desert-muted p-2 sm:p-3 rounded-md text-xs sm:text-sm;
  background-color: rgba(var(--desert-muted-rgb), 0.1);
}

.bookingSection {
  @apply border-t border-desert-muted pt-4 sm:pt-6;
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.bookingForm {
  @apply space-y-3 sm:space-y-4;
}

.errorMessage {
  @apply text-red-500 text-xs sm:text-sm mb-2;
}

.inputGrid {
  @apply grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4;
}

.inputLabel {
  @apply block text-xs sm:text-sm text-desert-muted mb-1;
}

.input {
  @apply w-full px-3 py-2 bg-white dark:bg-desert-dark border rounded-md text-sm;
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.bookButton {
  @apply w-full bg-desert-primary text-white px-4 py-2 rounded-md hover:bg-desert-accent transition-colors flex items-center justify-center text-sm;
}

.bookButtonDisabled {
  @apply opacity-50 cursor-not-allowed;
}

.loaderCustom {
  @apply mr-2 [&>div]:border-t-white [&>div]:border-white/20;
}

.messagesContainer {
  @apply space-y-3 sm:space-y-4 mb-3 sm:mb-4 max-h-48 sm:max-h-60 overflow-y-auto;
}

.messageFromUser {
  @apply flex flex-col items-end;
}

.messageFromOther {
  @apply flex flex-col items-start;
}

.messageHeader {
  @apply flex items-center gap-1 sm:gap-2 mb-1;
}

.messageSender {
  @apply text-xs text-desert-muted;
}

.messageTime {
  @apply text-xs text-desert-muted;
}

.messageContentUser {
  @apply rounded-lg px-3 sm:px-4 py-1.5 sm:py-2 bg-desert-primary text-white text-sm;
  max-width: 85%;
}

@media (min-width: 640px) {
  .messageContentUser {
    max-width: 80%;
  }
}

.messageContentOther {
  @apply rounded-lg px-3 sm:px-4 py-1.5 sm:py-2 bg-desert-muted text-desert-dark dark:text-desert-light text-sm;
  max-width: 85%;
  background-color: rgba(var(--desert-muted-rgb), 0.1);
}

@media (min-width: 640px) {
  .messageContentOther {
    max-width: 80%;
  }
}

.messageInputContainer {
  @apply flex gap-2;
}

.messageInput {
  @apply flex-1 px-3 py-2 bg-white dark:bg-desert-dark border rounded-md shadow-sm focus:border-desert-primary focus:ring-1 focus:ring-desert-primary text-sm;
  border-color: rgba(var(--desert-muted-rgb), 0.2);
}

.sendButton {
  @apply bg-desert-primary text-white px-3 sm:px-4 py-2 rounded-md hover:bg-desert-accent transition-colors flex-shrink-0;
}

/* Add these new styles for the passengers section */
.passengersSection {
  margin-top: 1.5rem;
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.sectionTitle {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: #111827;
}

.passengersList {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.passengerItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  border-radius: 0.375rem;
  background-color: #f9fafb;
}

.passengerInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  background-color: #6366f1;
}

.driverAvatar {
  background-color: #4f46e5;
}

.avatarImg {
  width: 100%;
  height: 100%;
  border-radius: 9999px;
  object-fit: cover;
}

.passengerName {
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.roleLabel {
  font-size: 0.75rem;
  background-color: #4f46e5;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
  margin-left: 0.5rem;
}

.youLabel {
  font-size: 0.75rem;
  background-color: #10b981;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
}

.passengerDetails {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.passengerSeats, .passengerSuitcases {
  font-size: 0.875rem;
  color: #4b5563;
}

.passengerStatus {
  font-size: 0.75rem;
  padding: 0.125rem 0.375rem;
  border-radius: 9999px;
}

.statusconfirmed {
  background-color: #d1fae5;
  color: #065f46;
}

.statuspending {
  background-color: #fef3c7;
  color: #92400e;
}

.statuscancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

.driverInfo {
  font-size: 0.875rem;
  color: #4b5563;
}

.noPassengers {
  text-align: center;
  padding: 1rem;
  color: #6b7280;
  font-style: italic;
}

.loadingContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem;
}

.loadingText {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .passengerItem {
    background-color: #374151;
  }
  
  .passengerIcon {
    color: #9ca3af;
  }
  
  .passengerDetails {
    color: #9ca3af;
  }
  
  .loadingText, .noPassengers {
    color: #9ca3af;
  }
}

/* Dark mode support for new elements */
@media (prefers-color-scheme: dark) {
  .driverInfo {
    color: #818cf8; /* Lighter indigo for dark mode */
  }
  
  .youLabel {
    color: #9ca3af;
  }
  
  .noOtherPassengers {
    color: #9ca3af;
    border-top: 1px dashed #4b5563;
  }
}
