/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from "react";
import { Modal } from "../Modal";
import { Calendar, Clock, Users, Briefcase, Send } from "lucide-react";
import { io, Socket } from "socket.io-client";
import axios from "axios";
import type { Ride, Message } from "../../types";
import styles from "./RideDetailsModal.module.css";
import { ParticipantsList } from "./ParticipantsList";
import { MessageSection } from "./MessageSection";
import { BookingForm } from "./BookingForm";
import { Loader } from "../Loader";
import { convertUTCToLocal } from '../../utils/convertUTCToLocal';

type RideDetailsModalProps = {
  isOpen: boolean;
  onClose: () => void;
  ride: Ride;
  userId?: string;
  userName?: string;
  token: string | null;
  bookings: any[];
  setRides: React.Dispatch<React.SetStateAction<Ride[]>>;
  setUserBookings: React.Dispatch<React.SetStateAction<any[]>>;
  onBookingSuccess?: (rideId: string, seatsBooked: number, suitcasesBooked: number) => void;
  region: string;
  apiUrl: string;
};

type Passenger = {
  id: string;
  name?: string;
  user?: {
    id?: string;
    name?: string;
  };
  seats: number;
  suitcases: number;
  status: string;
  avatar?: string;
  avatarColor?: string;
};

const API_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";
const SOCKET_URL = import.meta.env.VITE_API_URL || "http://localhost:5000";

export function RideDetailsModal({
  isOpen,
  onClose,
  ride,
  userId,
  userName,
  token,
  bookings,
  setRides,
  setUserBookings,
  onBookingSuccess,
  region,
  apiUrl = API_URL
}: RideDetailsModalProps) {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [messages, setMessages] = useState<Message[]>(ride.messages || []);
  const [hasBooked, setHasBooked] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);
  const [passengers, setPassengers] = useState<Passenger[]>([]);
  const [isLoadingPassengers, setIsLoadingPassengers] = useState(false);
  const [newMessage, setNewMessage] = useState("");
  const [selectedSeats, setSelectedSeats] = useState(1);
  const [selectedSuitcases, setSelectedSuitcases] = useState(1);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);

  // Check if user is the driver/owner of the ride
  const isDriver = ride.driver && ride.driver.id === userId;

  // Check if user has already booked this ride
  useEffect(() => {
    const hasUserBookedThisRide = bookings.some(
      (booking) => booking.rideId === ride.id
    );

    if (hasUserBookedThisRide || isDriver) {
      setHasBooked(true);
    }
  }, [bookings, ride.id, isDriver]);

  // Fetch passengers for this ride
  useEffect(() => {
    const fetchPassengers = async () => {
      if (!token || !ride.id) return;

      setIsLoadingPassengers(true);
      try {
        const response = await axios.get(
          `${apiUrl}/api/bookings/ride/${ride.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        // Format passengers data to ensure it has the right structure
        const formattedPassengers = response.data.map((booking: any) => ({
          id: booking.id,
          user: {
            id: booking.userId || booking.user?.id,
            name: booking.user?.name || 'Unknown User'
          },
          seats: booking.seats,
          suitcases: booking.suitcases,
          status: booking.status,
          avatar: booking.user?.avatar,
          avatarColor: booking.user?.avatarColor || getAvatarColor(booking.userId || booking.user?.id)
        }));

        setPassengers(formattedPassengers);
        console.log("Fetched passengers for posted ride:", formattedPassengers);
      } catch (error) {
        console.error("Failed to fetch passengers:", error);
      } finally {
        setIsLoadingPassengers(false);
      }
    };

    // Always fetch passengers when the modal is open and user is authenticated
    if (isOpen && token) {
      fetchPassengers();
    }
  }, [isOpen, ride.id, token, apiUrl]);

  // Add this helper function to generate avatar colors
  const getAvatarColor = (id: string | undefined): string => {
    if (!id) return '#6366f1'; // Default color

    // Simple hash function to generate a color based on user ID
    const hash = Array.from(id).reduce((acc, char) => {
      return char.charCodeAt(0) + ((acc << 5) - acc);
    }, 0);

    const h = Math.abs(hash) % 360;
    return `hsl(${h}, 70%, 50%)`;
  };

  // Initialize socket connection
  useEffect(() => {
    if (!isOpen || !hasBooked) return;

    const newSocket = io(SOCKET_URL, {
      auth: {
        token,
      },
      transports: ["websocket"],
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      reconnectionAttempts: Infinity,
    });

    newSocket.on("connect", () => {
      console.log("Socket connected");
      setSocketConnected(true);
    });

    newSocket.on("connect_error", (error) => {
      console.error("Socket connection error:", error);
      setSocketConnected(false);
    });

    newSocket.on("disconnect", () => {
      console.log("Socket disconnected");
      setSocketConnected(false);
    });

    setSocket(newSocket);

    return () => {
      newSocket.off("connect");
      newSocket.off("connect_error");
      newSocket.off("disconnect");
      newSocket.close();
    };
  }, [isOpen, hasBooked, token]);

  // Join and leave ride chat room
  useEffect(() => {
    if (socket && socketConnected && ride.id && hasBooked) {
      console.log(`Joining ride chat: ${ride.id}`);
      socket.emit("join-ride-chat", ride.id);

      return () => {
        console.log(`Leaving ride chat: ${ride.id}`);
        socket.emit("leave-ride-chat", ride.id);
      };
    }
  }, [socket, socketConnected, ride.id, hasBooked]);

  // Load existing messages - only if user has booked
  useEffect(() => {
    const fetchMessages = async () => {
      if (!token) return;

      try {
        const response = await axios.get(
          `${apiUrl}/api/messages/ride/${ride.id}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );
        setMessages(response.data);
      } catch (error) {
        console.error("Failed to fetch messages:", error);
      }
    };

    if (isOpen && ride.id && hasBooked) {
      fetchMessages();
    }
  }, [isOpen, ride.id, token, hasBooked, apiUrl]);

  // Listen for new messages
  useEffect(() => {
    if (!socket || !socketConnected || !hasBooked) return;

    const handleNewMessage = (message: Message) => {
      console.log("New message received:", message);
      setMessages((prev) => [...prev, message]);
    };

    socket.on("new-message", handleNewMessage);

    return () => {
      socket.off("new-message", handleNewMessage);
    };
  }, [socket, socketConnected, hasBooked]);

  // Handle sending a message
  const handleSendMessage = async () => {
    if (!newMessage.trim() || !userId || !ride.id || !socketConnected) return;

    try {
      await axios.post(
        `${apiUrl}/api/messages/send`,
        {
          rideId: ride.id,
          content: newMessage.trim(),
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setNewMessage("");
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  // Handle booking a ride
  const handleBookRide = async () => {
    if (!token || !ride.id) return;

    setIsBooking(true);
    setBookingError(null);

    try {
      const response = await axios.post(
        `${apiUrl}/api/bookings/bookRide`,
        {
          rideId: ride.id,
          seats: selectedSeats,
          suitcases: selectedSuitcases,
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // Update all states through the parent component
      if (onBookingSuccess) {
        onBookingSuccess(ride.id, selectedSeats, selectedSuitcases);
      }

      // Update userBookings state
      setUserBookings((prevBookings) => [
        ...prevBookings,
        {
          rideId: ride.id,
          seats: selectedSeats,
          suitcases: selectedSuitcases,
          ...response.data,
        },
      ]);

      setHasBooked(true);
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        "Failed to book the ride. Please try again.";
      setBookingError(errorMessage);
      console.error("Booking error:", error);
    } finally {
      setIsBooking(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <div className={styles.container}>
        <div className={styles.header}>
          <div>
            <h3 className={styles.title}>
              {ride.from.charAt(0).toUpperCase() + ride.from.slice(1)} →{" "}
              {ride.to.charAt(0).toUpperCase() + ride.to.slice(1)}
            </h3>
            <p className={styles.postedTime}>
              Posted {ride.postedTime}
            </p>
          </div>
          <div className={styles.priceContainer}>
            <div className={styles.price}>
              ${ride.price}
              <span className={styles.pricePerSeat}>/seat</span>
            </div>
          </div>
        </div>

        <div className={styles.detailsContainer}>
          <div className={styles.detailItem}>
            <Calendar className={styles.detailIcon} />
            <span>{convertUTCToLocal(ride.date, ride.time).localDate}</span>
          </div>
          <div className={styles.detailItem}>
            <Clock className={styles.detailIcon} />
            <span>Departing at {convertUTCToLocal(ride.date, ride.time).localTime}</span>
          </div>
          <div className={styles.detailItem}>
            <Users className={styles.detailIcon} />
            <span>
              {ride.seatsAvailable} of {ride.totalSeats} seats available
            </span>
          </div>
          <div className={styles.detailItem}>
            <Briefcase className={styles.detailIcon} />
            <span>
              {ride.suitcasesAvailable} of {ride.totalSuitcases} suitcases
              allowed
            </span>
          </div>
        </div>

        {ride.notes && ride.notes.length > 0 && (
          <div className={styles.notesSection}>
            <h4 className={styles.sectionTitle}>
              Trip Notes
            </h4>
            <div className={styles.notesList}>
              {ride.notes.map((note, index) => (
                <div
                  key={index}
                  className={styles.noteItem}
                >
                  {note}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Render participants section for drivers and passengers who have booked */}
        {(isDriver || hasBooked) && (
          <div className={styles.passengersSection}>
            <h4 className={styles.sectionTitle}>Ride Participants</h4>
            <ParticipantsList
              driver={ride.driver}
              passengers={passengers}
              isLoadingPassengers={isLoadingPassengers}
              userId={userId}
            />
          </div>
        )}

        <div className={styles.bookingSection}>
          {!hasBooked && !isDriver ? (
            <BookingForm
              rideId={ride.id}
              rideFrom={ride.from}
              rideTo={ride.to}
              rideDate={ride.date}
              rideTime={ride.time}
              seatsAvailable={ride.seatsAvailable}
              suitcasesAvailable={ride.suitcasesAvailable}
              token={token}
              apiUrl={apiUrl}
              onBookingSuccess={onBookingSuccess || (() => { })}
              setUserBookings={setUserBookings}
              setHasBooked={setHasBooked}
              ridePrice={ride.price}
            />
          ) : hasBooked || isDriver ? (
            <MessageSection
              messages={messages}
              userId={userId}
              rideId={ride.id}
              token={token}
              apiUrl={apiUrl}
              socketConnected={socketConnected}
            />
          ) : null}
        </div>
      </div>
    </Modal>
  );
}
