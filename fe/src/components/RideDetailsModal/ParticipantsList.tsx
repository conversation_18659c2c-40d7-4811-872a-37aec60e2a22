import { User } from "lucide-react";
import { Loader } from "../Loader";
import styles from "./RideDetailsModal.module.css";

type Passenger = {
  id: string;
  name?: string;
  user?: {
    id?: string;
    name?: string;
  };
  seats: number;
  suitcases: number;
  status: string;
  avatar?: string;
  avatarColor?: string;
};

type Driver = {
  id?: string;
  name?: string;
  avatar?: string;
  info?: string;
  avatarColor?: string;
};

type ParticipantsListProps = {
  driver?: Driver;
  passengers: Passenger[];
  isLoadingPassengers: boolean;
  userId?: string;
};

export function ParticipantsList({ 
  driver, 
  passengers, 
  isLoadingPassengers, 
  userId 
}: ParticipantsListProps) {
  // Helper function to get initials from name
  const getInitials = (name: string | undefined): string => {
    if (!name) return '?';
    
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  if (isLoadingPassengers) {
    return (
      <div className={styles.loadingContainer}>
        <Loader size="sm" />
        <span className={styles.loadingText}>Loading participants...</span>
      </div>
    );
  }

  return (
    <div className={styles.passengersList}>
      {/* Always show the driver first */}
      {driver && (
        <div className={styles.passengerItem}>
          <div className={styles.passengerInfo}>
            <div 
              className={`${styles.avatar} ${styles.driverAvatar}`}
              style={{ backgroundColor: driver.avatarColor || '#4f46e5' }}
            >
              {driver.avatar ? (
                <img src={driver.avatar} alt={driver.name || 'Driver'} className={styles.avatarImg} />
              ) : (
                <span>{getInitials(driver.name)}</span>
              )}
            </div>
            <span className={styles.passengerName}>
              {driver.name || 'Unknown Driver'}
              <span className={styles.roleLabel}>Driver</span>
              {driver.id === userId && <span className={styles.youLabel}>(You)</span>}
            </span>
          </div>
          <div className={styles.passengerDetails}>
            <span className={styles.driverInfo}>
              {driver.info || "Ride Owner"}
            </span>
          </div>
        </div>
      )}
      
      {/* Then show all passengers */}
      {passengers.length > 0 ? (
        passengers.map((passenger) => (
          <div key={passenger.id} className={styles.passengerItem}>
            <div className={styles.passengerInfo}>
              <div 
                className={styles.avatar}
                style={{ backgroundColor: passenger.avatarColor || '#6366f1' }}
              >
                {passenger.avatar ? (
                  <img src={passenger.avatar} alt={passenger.user?.name || passenger.name || 'Passenger'} className={styles.avatarImg} />
                ) : (
                  <span>{getInitials(passenger.user?.name || passenger.name)}</span>
                )}
              </div>
              <span className={styles.passengerName}>
                {passenger.user?.name || passenger.name || 'Unknown Passenger'}
                {(passenger.user?.id === userId) && <span className={styles.youLabel}>(You)</span>}
              </span>
            </div>
            <div className={styles.passengerDetails}>
              <span className={styles.passengerSeats}>
                {passenger.seats} {passenger.seats === 1 ? "seat" : "seats"}
              </span>
              <span className={styles.passengerSuitcases}>
                {passenger.suitcases} {passenger.suitcases === 1 ? "suitcase" : "suitcases"}
              </span>
              <span className={`${styles.passengerStatus} ${styles[`status${passenger.status.toLowerCase()}`]}`}>
                {passenger.status}
              </span>
            </div>
          </div>
        ))
      ) : (
        <div className={styles.noPassengers}>
          No passengers have booked this ride yet.
        </div>
      )}
    </div>
  );
}
