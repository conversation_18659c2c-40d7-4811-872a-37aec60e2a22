import { useState } from "react";
import axios from "axios";
import { Loader } from "../Loader";
import styles from "./RideDetailsModal.module.css";
import { PaymentForm } from "../PaymentForm";
import Modal from "react-modal";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import kamel<PERSON>ogo from "./kamel-logo.png";
import { convertUTCToLocal } from '../../utils/convertUTCToLocal';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);
const STRIPE_ENABLED = import.meta.env.VITE_STRIPE_ENABLED === 'true';

type BookingFormProps = {
  rideId: string;
  rideFrom: string
  rideTo: string
  rideDate: string;
  rideTime: string;
  seatsAvailable: number;
  suitcasesAvailable: number;
  token: string | null;
  apiUrl: string;
  onBookingSuccess: (rideId: string, seatsBooked: number, suitcasesBooked: number) => void;
  setUserBookings: React.Dispatch<React.SetStateAction<any[]>>;
  setHasBooked: React.Dispatch<React.SetStateAction<boolean>>;
  ridePrice: number;
};

function formatDate(dateStr: string, timeStr: string) {
  const { localDate } = convertUTCToLocal(dateStr, timeStr);
  return localDate;
}

function formatTime(timeStr: string, dateStr: string) {
  const { localTime12 } = convertUTCToLocal(dateStr, timeStr);
  return localTime12;
}

export function BookingForm({
  rideId,
  rideFrom,
  rideTo,
  rideDate,
  rideTime,
  seatsAvailable,
  suitcasesAvailable,
  token,
  apiUrl,
  onBookingSuccess,
  setUserBookings,
  setHasBooked,
  ridePrice
}: BookingFormProps) {
  const [selectedSeats, setSelectedSeats] = useState(1);
  const [selectedSuitcases, setSelectedSuitcases] = useState(1);
  const [isBooking, setIsBooking] = useState(false);
  const [bookingError, setBookingError] = useState<string | null>(null);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);

  const totalAmount = ridePrice * selectedSeats;

  // Show payment form on button click
  const handleShowPaymentForm = async () => {
    if (!STRIPE_ENABLED) {
      handleBookRide();
      return;
    }
    // Fetch clientSecret from backend
    try {
      const response = await axios.post(
        `${apiUrl}/api/payments/create-payment-intent`,
        {
          rideId,
          seats: selectedSeats,
          suitcases: selectedSuitcases,
        },
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      setClientSecret(response.data.clientSecret);
      setShowPaymentForm(true);
    } catch (error) {
      setBookingError("Could not start payment. Please try again.");
    }
  };

  const handleClosePaymentForm = () => {
    setShowPaymentForm(false);
  };

  // After card is added, proceed to booking
  const handlePaymentSuccess = () => {
    setShowPaymentForm(false);
    setHasBooked(true);
    handleBookRide();
  };

  const handleBookRide = async () => {
    if (!token || !rideId) return;

    setIsBooking(true);
    setBookingError(null);

    try {
      let paymentIntentId = null;
      if (STRIPE_ENABLED) {
        paymentIntentId = clientSecret?.split('_secret')[0];
      } else {
        paymentIntentId = "NO_STRIPE";
      }

      const response = await axios.post(
        `${apiUrl}/api/bookings/bookRide`,
        {
          rideId: rideId,
          seats: selectedSeats,
          suitcases: selectedSuitcases,
          paymentIntentId, // Always send, dummy value if Stripe is off
        },
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // Update all states through the parent component
      onBookingSuccess(rideId, selectedSeats, selectedSuitcases);

      // Update userBookings state
      setUserBookings((prevBookings) => [
        ...prevBookings,
        {
          rideId: rideId,
          seats: selectedSeats,
          suitcases: selectedSuitcases,
          ...response.data,
        },
      ]);

      setHasBooked(true);
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        "Failed to book the ride. Please try again.";
      setBookingError(errorMessage);
      console.error("Booking error:", error);
    } finally {
      setIsBooking(false);
    }
  };

  return (
    <div className={styles.bookingForm}>
      <h4 className={styles.sectionTitle}>Book this Ride</h4>

      {bookingError && (
        <div className={styles.errorMessage}>{bookingError}</div>
      )}

      <div className={styles.inputGrid}>
        <div>
          <label className={styles.inputLabel}>Seats needed</label>
          <input
            type="number"
            min="1"
            max={seatsAvailable}
            value={selectedSeats}
            onChange={(e) => setSelectedSeats(Number(e.target.value))}
            className={styles.input}
          />
        </div>
        <div>
          <label className={styles.inputLabel}>Suitcases needed</label>
          <input
            type="number"
            min="1"
            max={suitcasesAvailable}
            value={selectedSuitcases}
            onChange={(e) => setSelectedSuitcases(Number(e.target.value))}
            className={styles.input}
          />
        </div>
      </div>

      <button
        onClick={handleShowPaymentForm}
        disabled={isBooking}
        className={`${styles.bookButton} ${
          isBooking ? styles.bookButtonDisabled : ""
        }`}
      >
        {isBooking ? (
          <>
            <Loader size="sm" className={styles.loaderCustom} />
            <span>Booking...</span>
          </>
        ) : (
          "Book this Ride"
        )}
      </button>
      <Modal
        isOpen={showPaymentForm && STRIPE_ENABLED}
        onRequestClose={handleClosePaymentForm}
        contentLabel="Add Payment Method"
        ariaHideApp={false}
        style={{
          content: {
            maxWidth: "520px",
            width:"520px",
            maxHeight:"90vh",
            overflowY: "auto",
            inset: "50% auto auto 50%",
            transform: "translate(-50%, -50%)",
            borderRadius: "12px",
            padding: "2rem",
            zIndex: 2001,
            position: "absolute",
          },
          overlay: {
            backgroundColor: "rgba(0,0,0,0.5)",
            zIndex: 2000,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          },
        }}
      >
        <button
          onClick={handleClosePaymentForm}
          style={{
            position: "absolute",
            top: 24,
            right: 20,
            background: "transparent",
            border: "none",
            fontSize: "1.7rem",
            fontWeight: 700,
            color: "#888",
            cursor: "pointer",
            lineHeight: 1,
            padding: 0,
            zIndex: 10,
          }}
          aria-label="Close"
        >
          ×
        </button>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            gap: "1rem",
            marginBottom: "1.5rem",
          }}
        >
          <img
            src={kamelLogo}
            alt="Kamel Ride"
            style={{
              height: 48,
              width: 48,
              borderRadius: "50%",
              objectFit: "cover",
              boxShadow: "0 2px 8px rgba(0,0,0,0.07)",
              background: "#fff",
            }}
          />
          
          <span
            style={{
              fontWeight: 500,
              fontSize: "1.5rem",
              letterSpacing: 0.4,
              color: "#222",
              lineHeight: 1.1,
            }}
          >
            Kamel Ride
          </span>
        </div>

        <div style={{ marginBottom: "1.5rem" }}>
          <div style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 4
          }}>
            <span style={{
              fontWeight: 700,
              fontSize: "1.15rem",
              color: "#183153"
            }}>
              {rideFrom} &rarr; {rideTo}
            </span>
            <span style={{
              fontWeight: 700,
              fontSize: "1.1rem",
              color: "#183153"
            }}>
              {/* ${Number(ridePrice).toFixed(2)}/seat */}
            </span>
          </div>
          <div style={{ color: "#444", fontSize: "0.97rem" }}>
            <span style={{ marginRight: 12 }}>
              Date: {formatDate(rideDate, rideTime)}
            </span>
            <span>
              Time: {formatTime(rideTime, rideDate)}
            </span>
          </div>
          <hr style={{ margin: "0.5rem 0" }} />
        </div>

        {clientSecret ? (
          <>
            <p style={{ fontWeight: 600, marginBottom: "1rem", marginTop: "-0.8rem" }}>
              Amount to pay: ${totalAmount.toFixed(2)}
            </p>
            <Elements stripe={stripePromise} options={{ clientSecret }}>
              <PaymentForm onPaymentSuccess={handlePaymentSuccess} />
            </Elements>
          </>
        ) : (
          <Loader size="sm" />
        )}
      </Modal>
    </div>
  );
}