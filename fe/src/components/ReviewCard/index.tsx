import { useNavigate } from "react-router-dom";
const months = ['Jan', 'Feb', 'March', 'April', 'May', 'June', 'July', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

type Reviewer = {
  id: string;
  name: string;
  avatar: string;
};

type Ride = {
  id: string;
  from: string;
  to: string;
  date: string;
  time: string;
};

type Review = {
  id: string;
  rideId: string;
  bookingId: string;
  reviewerId: string;
  revieweeId: string;
  stars: number;
  comment: string | null;
  reviewType: 'driver' | 'passenger';
  createdAt: string;
  updatedAt: string;
  reviewer: Reviewer;
  ride: Ride;
};

type ReviewCardProps = {
    review : Review;
    reviewDate: Date;
}

export default function ReviewCard({review, reviewDate} : ReviewCardProps){

    const navigate = useNavigate();

    return(
        <div className = "border rounded-lg grid gap-2 p-10">
            <div className = "flex flex-row" onClick={() => navigate(`/profile/${review?.reviewer.id}`)}>
                <img
                src={review?.reviewer.avatar}
                alt={`${review?.reviewer.name}'s profile`}
                className="rounded-full w-8 h-8 mr-4 object-cover"
                />
                <div className = "font-bold">
                {review?.reviewer.name}
                </div>
            </div>

            <div className = "text-left">
                <div>
                    {'★'.repeat(review?.stars)}  - {reviewDate?months[reviewDate.getMonth()]: 'No dates'} {reviewDate? reviewDate.getDay(): null}, {reviewDate? reviewDate.getFullYear() : null}
                </div>
                <p>
                    {review?.comment}
                </p>
            </div>

        </div>
    )
}