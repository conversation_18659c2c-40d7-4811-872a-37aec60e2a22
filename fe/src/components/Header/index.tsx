import React, { useState } from 'react';
import { LogOut, Sun, Moon, Menu, X, Mail } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { PostRideModal } from '../PostRideModal';
import { RequestRideModal } from '../RequestRideModal';
// import { useTheme } from '../ThemeProvider';
import type { User } from '../../types';
import styles from './Header.module.css';
import logoImage from '/assets/Logo.png';
import { useStripeStatus } from '../../context/StripeStatusContext';

const STRIPE_ENABLED = import.meta.env.VITE_STRIPE_ENABLED === 'true';

type HeaderProps = {
  user: User | null;
  region: string;
  onSignInClick: () => void;
  onSignOutClick: () => void;
};

export function Header({ user, region, onSignInClick, onSignOutClick }: HeaderProps) {
  const [isPostRideModalOpen, setIsPostRideModalOpen] = useState(false);
  const [isRequestRideModalOpen, setIsRequestRideModalOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const stripeStatus = useStripeStatus();
  const navigate = useNavigate();
  // const { theme, toggleTheme } = useTheme();

  // Toggle mobile menu
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  // Close mobile menu
  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const handlePostRideClick = () => {
    if (!user) {
      onSignInClick();
    } else if (!stripeStatus?.payouts_enabled && STRIPE_ENABLED) {
      alert("You must connect your bank account on your Profile page before posting a ride.");
      // Optionally, you can redirect to the profile page:
      // navigate(`/profile`);
    } else {
      setIsPostRideModalOpen(true);
    }
    closeMobileMenu();
  };

  const handleRequestRideClick = () => {
    if (!user) {
      onSignInClick();
    } else if (!stripeStatus?.payouts_enabled && STRIPE_ENABLED) {
      alert("You must connect your bank account on your Profile page before posting a ride.");
      // Optionally, you can redirect to the profile page:
      // navigate(`/profile`);
    } else {
      setIsRequestRideModalOpen(true);
    }
    closeMobileMenu();
  };

  const handleMyRidesClick = () => {
    if (!user) {
      onSignInClick();
    } else {
      navigate(`/my-rides`);
    }
    closeMobileMenu();
  };

  const handleSignInClick = () => {
    onSignInClick();
    closeMobileMenu();
  };

  const handleSignOutClick = () => {
    onSignOutClick();
    closeMobileMenu();
  };

  const handleStoredUserProfileClick = () => {
    if (!user) {
      onSignInClick(); // Just to be clear - this line should never be accessed. Users should only be able to see their profile if they are logged in, and the <your user name> text, that allows you to go to your profile, only shows up if you are logged in. 
      // The line exists in case, somehow, this issue arises.
    } else {
      navigate(`/profile`);
    }
    closeMobileMenu();
  };

  const handleInboxClick = () => {
    if (!user) {
      onSignInClick();
    } else {
      navigate(`/inbox`);
    }
    closeMobileMenu();
  };

  const switchRegion = (newRegion: string) => {
    // Log out the user from the current region before switching
    if (user) {
      onSignOutClick();
    }

    // Clear any pending requests
    window.stop();

    // Navigate to the same page but in the new region
    const currentPath = window.location.pathname;
    const pathWithoutRegion = currentPath.replace(/^\/(cornell|nj)/, '');
    const newPath = `/${newRegion}${pathWithoutRegion || '/'}`;

    // Update the current region in localStorage
    localStorage.setItem('current_region', newRegion);

    // Use window.location.href to force a full page reload
    window.location.href = newPath;
  };


  // Determine theme classes based on region
  const regionTheme = region === 'nj' ? 'nj' : 'desert';
  const buttonClass = `bg-${regionTheme}-primary dark:bg-${regionTheme}-primary-dark text-white px-2 sm:px-4 py-2 rounded-md hover:bg-${regionTheme}-accent dark:hover:bg-${regionTheme}-accent-dark transition-colors text-sm sm:text-base whitespace-nowrap`;

  return (
    <>
      <header className={`bg-white dark:bg-${regionTheme === 'nj' ? 'nj' : 'desert'}-dark border-b`}>
        <div className={styles.container}>
          <div className={styles.flexContainer}>
            <div className={styles.logoContainer}>
              {/* `/` */}
              <a href={`/`} className={styles.logoLink}>
                <img
                  src={logoImage}
                  alt="Kamel Logo"
                  className={styles.logo}
                />
                <span className={styles.appName}>Kamel</span>
                <span className={styles.betaTag}>beta</span>
              </a>
            </div>

            {/* Desktop menu */}
            <div className={`${styles.actionsContainer} hidden md:flex`}>
              <button
                className={buttonClass}
                onClick={handleMyRidesClick}
              >
                My Rides
              </button>
              <button
                className={buttonClass}
                onClick={handleRequestRideClick}
              >
                Request a Ride
              </button>
              <button
                className={buttonClass}
                onClick={handlePostRideClick}
              >
                Post a Ride
              </button>
              <button
                className={styles.userName}
                onClick={handleInboxClick}
                style={{ display: 'flex', alignItems: 'center', gap: 6 }}
              >
                <Mail className="h-5 w-5 mr-1" /> Inbox
              </button>
              {user ? (
                <div className={styles.userContainer}>
                  <span
                    className={styles.userName}
                    onClick={handleStoredUserProfileClick}
                  >
                    {user.name}
                  </span>
                  <button
                    onClick={onSignOutClick}
                    className={styles.signOutButton}
                    aria-label="Sign out"
                  >
                    <LogOut className="h-5 w-5" />
                  </button>
                </div>
              ) : (
                <button
                  className={styles.signInButton}
                  onClick={onSignInClick}
                >
                  Sign in
                </button>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <button
                onClick={toggleMobileMenu}
                className={`p-2 text-${regionTheme}-muted hover:text-${regionTheme}-primary`}
                aria-label="Toggle menu"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className={`${styles.mobileMenu} md:hidden`}>
            <div className={styles.mobileMenuContent}>
              <button
                className={styles.mobileMenuItem}
                onClick={handleMyRidesClick}
              >
                My Rides
              </button>
              <button
                className={styles.mobileMenuItem}
                onClick={handleRequestRideClick}
              >
                Request a Ride
              </button>
              <button
                className={styles.mobileMenuItem}
                onClick={handlePostRideClick}
              >
                Post a Ride
              </button>
              <button
                className={styles.mobileMenuItem}
                onClick={handleInboxClick}
              >
                <Mail className="inline-block mr-2 h-5 w-5" /> Inbox
              </button>
              {user ? (
                <div className={styles.mobileUserContainer}>
                  <span className={styles.mobileUserName}>{user.name}</span>
                  <button
                    onClick={handleSignOutClick}
                    className={styles.mobileSignOutButton}
                  >
                    Sign out <LogOut className="h-4 w-4 ml-1" />
                  </button>
                </div>
              ) : (
                <button
                  className={styles.mobileSignInButton}
                  onClick={handleSignInClick}
                >
                  Sign in
                </button>
              )}
            </div>
          </div>
        )}
      </header>

      {/* Region banner */}
      {/* <div className={`py-2 px-4 text-center text-sm ${region === 'nj'
        ? 'bg-nj-secondary/20 text-nj-text'
        : 'bg-desert-secondary/20 text-desert-text'
        }`}>
        {region === 'nj' ? (
          <div className="flex justify-center items-center space-x-2">
            <span>Are you a Cornellian?</span>
            <button
              onClick={() => switchRegion('cornell')}
              className="px-3 py-1 rounded bg-desert-primary text-white hover:bg-desert-accent transition-colors"
            >
              Use our Cornell app
            </button>
          </div>
        ) : (
          <div className="flex justify-center items-center space-x-2">
            <span>Are you from New Jersey?</span>
            <button
              onClick={() => switchRegion('nj')}
              className="px-3 py-1 rounded bg-nj-primary text-white hover:bg-nj-accent transition-colors"
            >
              Use our NJ app
            </button>
          </div>
        )}
      </div> */}

      <PostRideModal
        isOpen={isPostRideModalOpen}
        onClose={() => setIsPostRideModalOpen(false)}
        region={region}
      />
      <RequestRideModal
        isOpen={isRequestRideModalOpen}
        onClose={() => setIsRequestRideModalOpen(false)}
      />
    </>
  );
}
