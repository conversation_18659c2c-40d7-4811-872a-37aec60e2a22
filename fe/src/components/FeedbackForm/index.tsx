import { useState, useEffect } from 'react';
import type { User } from '../../types';
import styles from './FeedbackForm.module.css';

type FeedbackFormProps = {
  user?: User | null;
};

export function FeedbackForm({ user }: FeedbackFormProps) {
  const [comment, setComment] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [error, setError] = useState('');

  // Pre-populate email when user is logged in
  useEffect(() => {
    if (user?.email) {
      setEmail(user.email);
    }
  }, [user]);

  const DUMMY_EMAIL = '<EMAIL>'; // Replace with real email later

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError('');
    try {
      // Simulate sending feedback (replace with real API/email logic)
      await new Promise((resolve) => setTimeout(resolve, 1000));
      // Optionally, POST to a placeholder endpoint here
      setSubmitted(true);
    } catch (err) {
      console.error('Error sending feedback:', err);
      setError('Failed to send feedback. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitted) {
    return (
      <div className={styles.successMessage}>
        <p className={styles.successTitle}>Thank you for your feedback!</p>
        <p className={styles.successText}>We appreciate your input and will use it to improve Kamel Ride.</p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className={styles.container}>
      <h2 className={styles.title}>Feedback</h2>
      <p className={styles.description}>Share your experience with Kamel Ride. Your feedback goes to <span className={styles.dummyEmail}>{DUMMY_EMAIL}</span>.</p>
      <div className={styles.formGroup}>
        <textarea
          className={styles.textarea}
          placeholder="Your comments..."
          value={comment}
          onChange={e => setComment(e.target.value)}
          required
          maxLength={1000}
        />
      </div>
      <div className={styles.formGroup}>
        <input
          type="email"
          className={styles.input}
          placeholder={user ? "Email (from your account)" : "Your email (optional)"}
          value={email}
          onChange={e => setEmail(e.target.value)}
        />
      </div>
      {error && <div className={styles.error}>{error}</div>}
      <button
        type="submit"
        className={styles.submitButton}
        disabled={isSubmitting || !comment.trim()}
      >
        {isSubmitting ? 'Sending...' : 'Submit Feedback'}
      </button>
    </form>
  );
}

export default FeedbackForm; 