'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('users', 'driverVerified', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
    await queryInterface.addColumn('users', 'verifiedUntil', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'verificationStatus', {
      type: Sequelize.ENUM('none', 'pending', 'approved', 'rejected'),
      allowNull: false,
      defaultValue: 'none',
    });
    await queryInterface.addColumn('users', 'verificationNotes', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'licenseFrontKey', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'licenseBackKey', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'insuranceKey', {
      type: Sequelize.STRING,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'licenseExpiration', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'insuranceExpiration', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'verificationSubmittedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('users', 'verificationReviewedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('users', 'driverVerified');
    await queryInterface.removeColumn('users', 'verifiedUntil');
    await queryInterface.removeColumn('users', 'verificationStatus');
    await queryInterface.removeColumn('users', 'verificationNotes');
    await queryInterface.removeColumn('users', 'licenseFrontKey');
    await queryInterface.removeColumn('users', 'licenseBackKey');
    await queryInterface.removeColumn('users', 'insuranceKey');
    await queryInterface.removeColumn('users', 'licenseExpiration');
    await queryInterface.removeColumn('users', 'insuranceExpiration');
    await queryInterface.removeColumn('users', 'verificationSubmittedAt');
    await queryInterface.removeColumn('users', 'verificationReviewedAt');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_users_verificationStatus";');
  }
};