import { syncModels } from '../models/index.js';
import { User } from '../models/userModel.js';
import bcrypt from 'bcryptjs';
import { getSequelize, testConnection } from './database.js';
import { Sequelize } from 'sequelize';

// Add this function to create schemas
const createSchemaIfNotExists = async (sequelize: Sequelize, schemaName: string) => {
  try {
    await sequelize.query(`CREATE SCHEMA IF NOT EXISTS ${schemaName}`);
    console.log(`Schema '${schemaName}' created or already exists`);
  } catch (error) {
    console.error(`Error creating schema '${schemaName}':`, error);
  }
};

// Initialize database and create admin user
export const initializeDatabase = async (force = false) => {
  try {
    // Use a single database connection
    const sequelize = getSequelize();
    
    // Create NJ schema if it doesn't exist
    await createSchemaIfNotExists(sequelize, 'nj');
    
    // Initialize public schema first
    console.log('Initializing public schema');
    await sequelize.query(`SET search_path TO public`);
    await sequelize.sync({ force });
    
    // Then initialize NJ schema
    console.log('Initializing nj schema');
    await sequelize.query(`SET search_path TO nj,public`);
    await sequelize.sync({ force });
    
    // Check if admin user exists
    const adminEmail = `<EMAIL>`;
    let admin = await User.findOne({ 
      where: { email: adminEmail },
      logging: false
    });

    if (!admin) {
      // Create admin user
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash('Admin123!', salt);

      admin = await User.create({
        name: `Admin (NJ)`,
        email: adminEmail,
        password: hashedPassword,
        role: 'admin',
      });

      console.log(`Admin user for NJ created successfully`);
    }
    
    console.log(`Schema for NJ initialized successfully`);
  } catch (error) {
    console.error('Database initialization error:', error);
    throw error;
  }
};

// Run if this file is executed directly
if (process.argv[1] === new URL(import.meta.url).pathname) {
  const force = process.argv.includes('--force');
  
  initializeDatabase(force)
    .then(() => {
      console.log('Database initialization completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Database initialization failed:', error);
      process.exit(1);
    });
}
