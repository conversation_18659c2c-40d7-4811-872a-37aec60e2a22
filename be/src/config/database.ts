import { Sequelize } from 'sequelize';
import dotenv from 'dotenv';
import { getRequestContext } from '../utils/requestContext.js';

// Load environment variables
dotenv.config();

// Single sequelize instance
let sequelizeInstance: Sequelize;

// Function to get the Sequelize instance
export const getSequelize = () => {
  if (!sequelizeInstance) {
    // Get database configuration
    const dbHost = process.env.DB_HOST || 'localhost';
    const dbPort = parseInt(process.env.DB_PORT || '5432');
    const dbName = process.env.DB_NAME || 'kamelride';
    const dbUser = process.env.DB_USER || 'postgres';
    const dbPassword = process.env.DB_PASSWORD || 'postgres';

    // Create and return a new Sequelize instance
    sequelizeInstance = new Sequelize(dbName, dbUser, dbPassword, {
      host: dbHost,
      port: dbPort,
      dialect: 'postgres',
      logging: (sql, timing) => {
        if (sql.includes('SELECT 1+1')) return; // Skip connection test logs
        console.log(`Executing query: ${sql}`);
      },
      dialectOptions: {
        ssl: {
          require: false,
          rejectUnauthorized: false
        }
      },
      define: {
        timestamps: true,
      },
    });
  }
  
  return sequelizeInstance;
};

// Function to get the current schema based on request context
export const getCurrentSchema = () => {
  const context = getRequestContext();
  if (!context) console.warn('[getCurrentSchema] No context found!');
  return context?.region === 'nj' ? 'nj' : 'public';
};

// Function to set the schema for the current request
export const setSchemaForRequest = async () => {
  const schema = getCurrentSchema();
  const sequelize = getSequelize();
  
  // Execute raw SQL to set the search path to ONLY the current schema
  await sequelize.query(`SET search_path TO ${schema}`);
  
  console.log(`Set search_path to ${schema}`);
  return sequelize;
};

// Default sequelize instance for backward compatibility
export const sequelize = getSequelize();

// Function to test database connection
export const testConnection = async (region: string = 'cornell') => {
  try {
    const sequelize = getSequelize();
    await sequelize.authenticate();
    await sequelize.query(`SET search_path TO ${region}`);
    console.log(`Database connection for region '${region}' has been established successfully.`);
    return true;
  } catch (error) {
    console.error(`Unable to connect to the database for region '${region}':`, error);
    return false;
  }
};
