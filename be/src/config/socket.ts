import { Server as SocketIOServer } from "socket.io";
import { Server as HTTPServer } from "http";
import { User } from "../models/userModel.js";

export let io: SocketIOServer;

export const initializeSocket = (server: HTTPServer) => {
  // Determine the allowed origins based on environment
  const allowedOrigins = process.env.NODE_ENV === 'production' 
    ? ["https://app.kamelride.com"] 
    : ["http://localhost:5173", "http://localhost:3000", "http://127.0.0.1:5173"];

  io = new SocketIOServer(server, {
    cors: {
      origin: allowedOrigins,
      methods: ["GET", "POST"],
      credentials: true,
    },
  });

  io.on("connection", (socket) => {
    console.log("User connected:", socket.id);

    // Join a ride's chat room
    socket.on("join-ride-chat", (rideId: string) => {
      console.log(`User ${socket.id} joined ride-${rideId}`);
      socket.join(`ride-${rideId}`);
    });

    // Leave a ride's chat room
    socket.on("leave-ride-chat", (rideId: string) => {
      console.log(`User ${socket.id} left ride-${rideId}`);
      socket.leave(`ride-${rideId}`);
    });

    // Optional: Handle send-message event if you want to support direct socket messages
    socket.on(
      "send-message",
      async (data: { rideId: string; content: string }) => {
        // For now, the REST API handles all message sending
        console.log(`Message received through socket for ride-${data.rideId}`);
      }
    );

    socket.on("disconnect", () => {
      console.log("User disconnected:", socket.id);
    });
  });

  return io;
};
