import { Request, Response } from 'express';
import { Booking, BookingStatus } from '../models/bookingModel.js';
import { Ride } from '../models/rideModel.js';
import { User } from '../models/userModel.js';
import { asyncHandler, AppError } from '../middleware/errorMiddleware.js';
import { Op } from 'sequelize';

import Notification from '../models/notification.js';
import { sendKlaviyoEvent } from '../utils/klaviyo.js';
import { sendDynamicEmail } from '../utils/sendGrid.js';
import Stripe from "stripe";
import { toZonedTime, format } from 'date-fns-tz';

// const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-07-30.basil" });
const STRIPE_ENABLED = process.env.STRIPE_ENABLED === 'true';
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-07-30.basil" });

// Helper function to calculate refund percentage based on time until ride
function getRefundPercentage(rideDate: string, rideTime: string): number {
  const rideDateTime = new Date(`${rideDate}T${rideTime || '00:00'}:00Z`);
  const now = new Date();
  const diffMs = rideDateTime.getTime() - now.getTime();
  const diffHours = diffMs / (1000 * 60 * 60);

  if (diffHours > 72) return 1.0; // More than 72 hrs before ride --> 100% refund
  if (diffHours > 24) return 0.5; // Between 24 and 72 hrs before ride --> 50% refund
  if (diffHours > 0) return 0.0;  // Between 0 and 24 hours before ride --> no refund
  return 0.0; // Past ride, no refund
}

// @desc    Create a new booking
// @route   POST /api/bookings
// @access  Private
export const createBooking = asyncHandler(async (req: Request, res: Response) => {
  const { rideId, seats, suitcases, paymentIntentId } = req.body;
  const user = req.user;

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find ride by ID
  const ride = await Ride.findByPk(rideId);

  if (!ride) {
    throw new AppError('Ride not found', 404);
  }

  // Check if ride has enough available seats and suitcases
  if (ride.seatsAvailable < seats) {
    throw new AppError(`Not enough seats available. Only ${ride.seatsAvailable} left.`, 400);
  }

  if (ride.suitcasesAvailable < suitcases) {
    throw new AppError(`Not enough space for suitcases. Only ${ride.suitcasesAvailable} left.`, 400);
  }

  // Check if user is not booking their own ride
  if (ride.driverId === user.id) {
    throw new AppError('You cannot book your own ride', 400);
  }

  // Total price of the ride
  const totalAmount = ride.price * seats * 100; // Converted to cents for Stripe

  // Create new booking
  const booking = await Booking.create({
    userId: user.id,
    rideId,
    seats,
    suitcases,
    paymentIntentId: STRIPE_ENABLED ? paymentIntentId : 'NO_STRIPE',
    status: BookingStatus.PENDING,
    passengerHasReviewed: false,
    driverHasReviewed: false,
    reminderSent: false,
    totalAmount: totalAmount,
    platformFeePercentage: 0.00,
    driverPaymentProcessed: false,
  });

  // Update ride's available seats and suitcases
  ride.seatsAvailable -= seats;
  ride.suitcasesAvailable -= suitcases;
  await ride.save();

  // SendGrid email notification to driver
  const driver = await User.findByPk(ride.driverId);


  console.log('Checking if driver email and template ID exist:', driver?.email, process.env.SENDGRID_NEW_PASSENGER_BOOKING_TEMPLATE_ID);
  if (driver && driver.email && process.env.SENDGRID_NEW_PASSENGER_BOOKING_TEMPLATE_ID) {
    try {
      console.log('Sending email from:', '<EMAIL>', 'to:', driver.email, 'with template:', process.env.SENDGRID_NEW_PASSENGER_BOOKING_TEMPLATE_ID);

      const estTimezone = 'America/New_York';
      const rideDateTimeUTC = new Date(`${ride.date}T${ride.time || '00:00'}:00Z`);
      const estDate = toZonedTime(rideDateTimeUTC, estTimezone);
      const formattedDate = format(estDate, 'EEEE, MMM d, yyyy');

      await sendDynamicEmail({
        to: driver.email,
        templateId: process.env.SENDGRID_NEW_PASSENGER_BOOKING_TEMPLATE_ID,
        dynamicTemplateData: {
          driverName: driver.name,
          passengerName: user.name,
          from: ride.from,
          to: ride.to,
          date: formattedDate,
        },
      });
      console.log(`Booking email sent to driver: ${driver.email}`);
    } catch (err) {
      console.error('Failed to send booking email to driver:', err);
    }
  }

  res.status(201).json(booking);
});

// @desc    Get all user's bookings
// @route   GET /api/bookings
// @access  Private
export const getUserBookings = asyncHandler(async (req: Request, res: Response) => {
  const user = req.user;

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Get user's bookings
  const bookings = await Booking.findAll({
    where: { userId: user.id },
    include: [
      {
        model: Ride,
        as: 'ride',
        include: [
          {
            model: User,
            as: 'driver',
            attributes: ['id', 'name', 'avatar'],
          },
        ],
      },
    ],
    order: [['createdAt', 'DESC']],
  });

  res.status(200).json(bookings);
});

export const getBookingByUserId = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.params.userId; 

  const booking = await Booking.findAll({
    where: {
      userId : userId
    }, 
    include : [
      {
        model : Ride, 
        as: 'ride',
        include: [
          {
            model: User, 
            as: 'driver', 
            attributes : ['id', 'name', 'avatar'],
          },
        ]
      },
      {
        model: User,
        as : 'user',
        attributes : ['id', 'name', 'avatar']
      }
    ]
  })

  if (!booking){
    throw new AppError('Booking not found', 404)
  }

  res.status(200).json(booking)
})

// @desc    Get booking by ID
// @route   GET /api/bookings/:id
// @access  Private
export const getBookingById = asyncHandler(async (req: Request, res: Response) => {
  const bookingId = req.params.id;
  const user = req.user;

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find booking by ID
  const booking = await Booking.findByPk(bookingId, {
    include: [
      {
        model: Ride,
        as: 'ride',
        include: [
          {
            model: User,
            as: 'driver',
            attributes: ['id', 'name', 'avatar'],
          },
        ],
      },
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'avatar'],
      },
    ],
  });

  if (!booking) {
    throw new AppError('Booking not found', 404);
  }

  // Check if user is authorized to view this booking
  const ride = booking.get('ride') as Ride;
  if (booking.userId !== user.id && ride.driverId !== user.id && user.role !== 'admin') {
    throw new AppError('Not authorized to view this booking', 403);
  }

  res.status(200).json(booking);
});

// @desc    Update booking status
// @route   PUT /api/bookings/:id/status
// @access  Private
export const updateBookingStatus = asyncHandler(async (req: Request, res: Response) => {
  const bookingId = req.params.id;
  const { status } = req.body;
  const user = req.user;

  console.log('updateBookingStatus called with status:', status);

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find booking by ID
  const booking = await Booking.findByPk(bookingId, {
    include: [
      {
        model: Ride,
        as: 'ride',
      },
    ],
  });

  if (!booking) {
    throw new AppError('Booking not found', 404);
  }

  // Prevent changing status if already final
  if (booking.status === BookingStatus.CONFIRMED || booking.status === BookingStatus.CANCELLED || booking.status === BookingStatus.COMPLETED) {
    throw new AppError('This booking has already been accepted/rejected or completed and cannot be changed.', 400);
  }

  const ride = booking.get('ride') as Ride;

  // Check if user is authorized to update this booking
  const isDriver = ride.driverId === user.id;
  const isBooker = booking.userId === user.id;
  const isAdmin = user.role === 'admin';

  if (!isDriver && !isBooker && !isAdmin) {
    throw new AppError('Not authorized to update this booking', 403);
  }

  // Only driver or admin can confirm bookings
  if (status === BookingStatus.CONFIRMED && !isDriver && !isAdmin) {
    throw new AppError('Only the driver can confirm bookings', 403);
  }

  // Update booking status
  const previousStatus: BookingStatus = booking.status;
  booking.status = status;
  await booking.save();

  // Stripe manual capture/cancel logic
  if (status === BookingStatus.CONFIRMED && previousStatus === BookingStatus.PENDING) {
    if (STRIPE_ENABLED) {
      try {
        await stripe.paymentIntents.capture(booking.paymentIntentId);
        console.log("PaymentIntent captured:", booking.paymentIntentId);
      } catch (err) {
        console.error("Failed to capture PaymentIntent:", err);
        throw new AppError('Failed to capture payment', 500);
      }
    }
  }

  if (status === BookingStatus.CANCELLED && previousStatus === BookingStatus.PENDING) {
    if (STRIPE_ENABLED) {
      try {
        await stripe.paymentIntents.cancel(booking.paymentIntentId);
        console.log("PaymentIntent cancelled:", booking.paymentIntentId);
      } catch (err) {
        console.error("Failed to cancel PaymentIntent:", err);
        // Not throwing here so cancellation can still proceed
      }
    }
  }

  // Notify passenger if driver accepted or rejected the booking
  const passenger = await User.findByPk(booking.userId);
  const driver = await User.findByPk(ride.driverId);

  if (passenger && passenger.email && driver && driver.name) {
    let templateId = '';
    let action = '';

    if (status === BookingStatus.CONFIRMED && process.env.SENDGRID_PASSENGER_ACCEPTED_TEMPLATE_ID) {
      templateId = process.env.SENDGRID_PASSENGER_ACCEPTED_TEMPLATE_ID;
      action = 'accepted';
    } else if (status === BookingStatus.CANCELLED && process.env.SENDGRID_PASSENGER_REJECTED_TEMPLATE_ID) {
      templateId = process.env.SENDGRID_PASSENGER_REJECTED_TEMPLATE_ID;
      action = 'rejected';
    }

    // Confirmation or rejection email
    if (templateId) {
      try {
        const rideDateTimeUTC = new Date(`${ride.date}T${ride.time}:00Z`);
        const estTimezone = 'America/New_York';
        const estDate = toZonedTime(rideDateTimeUTC, estTimezone);
        const formattedDate = format(estDate, 'EEEE, MMM d, yyyy');

        await sendDynamicEmail({
          to: passenger.email,
          templateId,
          dynamicTemplateData: {
            passengerName: passenger.name,
            driverName: driver.name,
            from: ride.from,
            to: ride.to,
            date: formattedDate,
          },
        });
        console.log(`Passenger notified by email: booking ${action}`);
      } catch (err) {
        console.error('Failed to send passenger status email:', err);
      }
    }

    // Receipt email if booking confirmed
    if (status === BookingStatus.CONFIRMED && process.env.SENDGRID_PASSENGER_RECEIPT_TEMPLATE_ID) {
      try {
        const rideDateTimeUTC = new Date(`${ride.date}T${ride.time}:00Z`);
        const estTimezone = 'America/New_York';
        const estDate = toZonedTime(rideDateTimeUTC, estTimezone);
        const formattedDate = format(estDate, 'EEEE, MMM d, yyyy');
        const formattedTime = format(estDate, 'hh:mm a zzz');
        const transactionDateEST = toZonedTime(booking.updatedAt, estTimezone);
        const formattedTransactionDate = format(transactionDateEST, 'EEEE, MMM d, yyyy hh:mm a');


        await sendDynamicEmail({
          to: passenger.email,
          templateId: process.env.SENDGRID_PASSENGER_RECEIPT_TEMPLATE_ID,
          dynamicTemplateData: {
            passengerName: passenger.name,
            driverName: driver.name,
            from: ride.from,
            to: ride.to,
            date: formattedDate,
            time: formattedTime,
            totalAmount: (booking.totalAmount/100).toFixed(2),
            seats: booking.seats,
            suitcases: booking.suitcases,
            bookingId: booking.id,
            rideId: ride.id,
            transactionDate: formattedTransactionDate,
          },
        });
        console.log('Passenger notified by email: receipt sent');
      } catch (err) {
        console.error('Failed to send passenger receipt email:', err);
      }
    }
  }

  // If cancelling a confirmed booking, restore seats and suitcases
  // @ts-ignore
  if (status === BookingStatus.CANCELLED && (previousStatus === BookingStatus.CONFIRMED || previousStatus === BookingStatus.PENDING)) {
    ride.seatsAvailable += booking.seats;
    ride.suitcasesAvailable += booking.suitcases;
    await ride.save();
  }

  // @ts-ignore
  if (status === BookingStatus.COMPLETED && previousStatus === BookingStatus.CANCELLED) {
    ride.seatsAvailable -= booking.seats;
    ride.suitcasesAvailable -= booking.suitcases;
    await ride.save();
  }

  res.status(200).json(booking);
});

// @desc    Cancel booking
// @route   DELETE /api/bookings/:id
// @access  Private
export const cancelBooking = asyncHandler(async (req: Request, res: Response) => {
  const bookingId = req.params.id;
  const user = req.user;

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find booking by ID
  const booking = await Booking.findByPk(bookingId, {
    include: [
      {
        model: Ride,
        as: 'ride',
      },
    ],
  });

  if (!booking) {
    throw new AppError('Booking not found', 404);
  }

  // Check if user is authorized to cancel this booking
  if (booking.userId !== user.id && user.role !== 'admin') {
    throw new AppError('Not authorized to cancel this booking', 403);
  }

  // Check if booking can be cancelled
  if (booking.status === BookingStatus.COMPLETED) {
    throw new AppError('Cannot cancel a completed booking', 400);
  }

  // Cancel PaymentIntent if booking is pending and Stripe is enabled
  if (
    booking.status === BookingStatus.PENDING &&
    STRIPE_ENABLED &&
    booking.paymentIntentId &&
    booking.paymentIntentId !== 'NO_STRIPE'
  ) {
    try {
      await stripe.paymentIntents.cancel(booking.paymentIntentId);
      console.log('PaymentIntent cancelled due to passenger cancellation:', booking.paymentIntentId);
    } catch (err) {
      console.error('Failed to cancel PaymentIntent on passenger cancellation:', err);
      // Not throwing here so cancellation can still proceed
    }
  }

  // Cancel PaymentIntent and issue partial refund if booking is confirmed
  if (
    booking.status === BookingStatus.CONFIRMED &&
    STRIPE_ENABLED &&
    booking.paymentIntentId &&
    booking.paymentIntentId !== 'NO_STRIPE'
  ) {
    // Calculate refund percentage based on time until ride
    const ride = booking.get('ride') as Ride;
    const refundPercentage = getRefundPercentage(ride.date, ride.time);

    if (refundPercentage > 0) {
      // Partial refund
      const refundAmount = Math.round(booking.totalAmount * refundPercentage);
      try {
        await stripe.refunds.create({
          payment_intent: booking.paymentIntentId,
          amount: refundAmount,
          reason: 'requested_by_customer',
        });
        console.log(`Partial refund (${refundPercentage * 100}%) issued: $${refundAmount / 100}`);
      } catch (err) {
        console.error('Failed to issue partial refund:', err);
        // Not throwing so cancellation can proceed
      }
    } else {
      // No refund
      console.log('No refund issued due to late cancellation.');
    }
  }

  // Update ride's available seats and suitcases if booking was confirmed
  if (booking.status === BookingStatus.CONFIRMED || booking.status === BookingStatus.PENDING) {
    const ride = booking.get('ride') as Ride;
    ride.seatsAvailable += booking.seats;
    ride.suitcasesAvailable += booking.suitcases;
    await ride.save();
  }

  // Update booking status to cancelled
  booking.status = BookingStatus.CANCELLED;
  await booking.save();

  // Fetch the ride and driver
  const ride = await Ride.findByPk(booking.rideId, {
    include: [{ model: User, as: 'driver' }]
  });

  let notificationError = null;
  if (ride && ride.driverId) {
    try {
      await Notification.create({
        senderId: booking.userId,
        recipientId: ride.driverId,
        title: 'Passenger Cancelled Booking',
        message: `Passenger ${user.name} cancelled their booking for your ride from ${ride.from} to ${ride.to} on ${ride.date}.`,
        type: 'cancellation'
      });

      const driver = await User.findByPk(ride.driverId);

      // SendGrid email notification
      if (!process.env.SENDGRID_PASSENGER_CANCEL_TEMPLATE_ID) {
        throw new Error('SENDGRID_PASSENGER_CANCEL_TEMPLATE_ID is not set in environment variables');
      }
      if (driver && driver.email) {
        const estTimezone = 'America/New_York';
        const rideDateTimeUTC = new Date(`${ride.date}T${ride.time || '00:00'}:00Z`);
        const estDate = toZonedTime(rideDateTimeUTC, estTimezone);
        const formattedDate = format(estDate, 'EEEE, MMM d, yyyy');

        await sendDynamicEmail({
          to: driver.email,
          templateId: process.env.SENDGRID_PASSENGER_CANCEL_TEMPLATE_ID,
          dynamicTemplateData: {
            driverName: driver.name,
            passengerName: user.name,
            from: ride.from,
            to: ride.to,
            date: formattedDate,
          },
        });
      }
    } catch (err: any) {
      notificationError = err;
      if (err.response && err.response.body && err.response.body.errors) {
        // Print each error message from SendGrid
        console.error('SendGrid error details:');
        for (const error of err.response.body.errors) {
          console.error(`- ${error.message}`);
          if (error.field) {
            console.error(`  Field: ${error.field}`);
          }
          if (error.help) {
            console.error(`  Help: ${error.help}`);
          }
        }
      } else {
        console.error('Notification or email failed:', err);
      }
    }
  }

  res.status(200).json({
    message: 'Booking cancelled successfully',
    notificationError: notificationError ? notificationError.toString() : undefined
  });
});

// @desc    Get bookings for a ride (driver and passengers)
// @route   GET /api/bookings/ride/:rideId
// @access  Private
export const getRideBookings = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.rideId;
  const user = req.user;


  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Find ride by ID
  const ride = await Ride.findByPk(rideId);

  if (!ride) {
    throw new AppError('Ride not found', 404);
  }

  // Check if user is the ride owner
  const isDriver = ride.driverId === user.id;
  const isAdmin = user.role === 'admin';

  // Check if user has booked this ride (is a passenger)
  const userBooking = await Booking.findOne({
    where: {
      rideId,
      userId: user.id,
      status: {
        [Op.in]: [BookingStatus.PENDING, BookingStatus.CONFIRMED]
      }
    }
  });

  const isPassenger = !!userBooking;

  // console.log('User:', user);
  // console.log('Ride:', ride);
  // console.log('isDriver:', isDriver, 'isAdmin:', isAdmin, 'isPassenger:', isPassenger);
  
  // Allow access if user is driver, admin, or has a booking for this ride
  if (!isDriver && !isAdmin && !isPassenger) {
    console.error('Authorization failed for user:', user.id, 'on ride:', rideId);
    throw new AppError('Not authorized to view bookings for this ride', 403);
  }

  // Get bookings for the ride
  const bookings = await Booking.findAll({
    where: { rideId },
    include: [
      {
        model: User,
        as: 'user',
        attributes: ['id', 'name', 'avatar'],
      },
    ],
    order: [['createdAt', 'DESC']],
  });

  // console.log('Ride bookings:', JSON.stringify(bookings, null, 2));

  res.status(200).json(bookings);
});