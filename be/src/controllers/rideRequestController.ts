import { Request, Response } from "express";
import { Op } from "sequelize";
import { RideRequest } from "../models/rideRequestModel.js";
import { User } from "../models/userModel.js";
import { asyncHandler, AppError } from "../middleware/errorMiddleware.js";
import { get } from "http";

// @desc    Create a new ride
// @route   POST /api/riderequests
// @access  Private
export const createRideRequest = asyncHandler(async (req: Request, res: Response) => {
  const { from, to, date, time, seatsNeeded, suitcasesNeeded, notes, 
    preferredPickupAddress, preferredPickupLat, preferredPickupLng,
    preferredDropoffAddress, preferredDropoffLat, preferredDropoffLng 
  } = req.body;

  const user = req.user;

  if (!user) {
    throw new AppError("User not found", 404);
  }

  // Create new ride
  const riderequest = await RideRequest.create({
    from,
    to,
    date,
    time,
    seatsNeeded,
    suitcasesNeeded,
    notes: notes || [],
    userId: user.id,
    preferredPickupAddress,
    preferredPickupLat,
    preferredPickupLng,
    preferredDropoffAddress,
    preferredDropoffLat,
    preferredDropoffLng,
  });

  res.status(201).json(riderequest);
});

// @desc    Get all rides with filters
// @route   GET /api/riderequests
// @access  Public
export const getRideRequests = asyncHandler(async (req: Request, res: Response) => {
  const { from, to, date, minSeats, minSuitcases } = req.query;

  // Build filter object
  const filter: any = {};

  if (from) {
    filter.from = from;
  }

  if (to) {
    filter.to = to;
  }

  if (date) {
    filter.date = date;
  }

  if (minSeats) {
    filter.seatsNeeded = { [Op.gte]: parseInt(minSeats as string) };
  }

  if (minSuitcases) {
    filter.suitcasesNeeded = { [Op.gte]: parseInt(minSuitcases as string) };
  }

  // Get rides with filters
  const rides = await RideRequest.findAll({
    where: {
      ...filter,
      [Op.and]: {
        date: {
          [Op.gte]: new Date(),
        },
      },
    },
    include: [
      {
        model: User,
        as: "passenger",
        attributes: ["id", "name", "avatar"],
      },
    ],
    order: [
      ["date", "ASC"],
      ["time", "ASC"],
    ],
  });

  // Format rides for frontend
  const formattedRides = rides.map((ride) => {
    const passenger = ride.get("passenger") as User;

    return {
      id: ride.id,
      from: ride.from,
      to: ride.to,
      date: ride.date,
      time: ride.time,
      seatsNeeded: ride.seatsNeeded,
      suitcasesNeeded: ride.suitcasesNeeded,
      postedTime: ride.postedTime,
      notes: ride.notes,
      passenger: passenger ? {
        id: passenger.id,
        name: passenger.name,
        avatar: passenger.avatar,
        avatarColor: getAvatarColor(passenger.id)
      } : null,
      preferredPickupAddress: ride.preferredPickupAddress,
      preferredPickupLat: ride.preferredPickupLat,
      preferredPickupLng: ride.preferredPickupLng,
      preferredDropoffAddress: ride.preferredDropoffAddress,
      preferredDropoffLat: ride.preferredDropoffLat,
      preferredDropoffLng: ride.preferredDropoffLng,
    };
  });

  res.status(200).json(formattedRides);
});

// @desc    Get ride by ID
// @route   GET /api/riderequests/:id
// @access  Public
export const getRideRequestById = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.id;

  // Find ride by ID
  const ride = await RideRequest.findByPk(rideId, {
    include: [
      {
        model: User,
        as: "passenger",
        attributes: ["id", "name", "avatar"],
      },
    ],
  });

  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  // Format ride for frontend
  const passenger = ride.get("passenger") as User;

  const formattedRide = {
    id: ride.id,
    from: ride.from,
    to: ride.to,
    date: ride.date,
    time: ride.time,
    seatsNeeded: ride.seatsNeeded,
    suitcasesNeeded: ride.suitcasesNeeded,
    postedTime: ride.postedTime,
    notes: ride.notes,
    passenger: {
      id: passenger.id,
      name: passenger.name,
      avatarColor: getAvatarColor(passenger.id),
      avatar: passenger.avatar,
    },
    preferredPickupAddress: ride.preferredPickupAddress,
    preferredPickupLat: ride.preferredPickupLat,
    preferredPickupLng: ride.preferredPickupLng,
    preferredDropoffAddress: ride.preferredDropoffAddress,
    preferredDropoffLat: ride.preferredDropoffLat,
    preferredDropoffLng: ride.preferredDropoffLng,
    // driver: {
    //   name: driver.name,
    //   info: "Cornell Verified driver",
    //   avatarColor: getAvatarColor(driver.id),
    // },
  };

  res.status(200).json(formattedRide);
});

// @desc    Update ride
// @route   PUT /api/riderequests/:id
// @access  Private
export const updateRideRequest = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.id;
  const user = req.user;

  if (!user) {
    throw new AppError("User not found", 404);
  }

  // Find ride by ID
  const ride = await RideRequest.findByPk(rideId);

  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  // Check if user is the ride owner
  //   if (ride.driverId !== user.id && user.role !== "admin") {
  //     throw new AppError("Not authorized to update this ride", 403);
  //   }

  // Update ride data
  const { from, to, date, time, seatsNeeded, suitcasesNeeded, notes,
    preferredPickupAddress, preferredPickupLat, preferredPickupLng,
    preferredDropoffAddress, preferredDropoffLat, preferredDropoffLng
   } =
    req.body;

  if (from) ride.from = from;
  if (to) ride.to = to;
  if (date) ride.date = date;
  if (time) ride.time = time;

  if (seatsNeeded) {
    ride.seatsNeeded = seatsNeeded;
  }

  if (suitcasesNeeded) {
    ride.suitcasesNeeded = suitcasesNeeded
  }

  if (preferredPickupAddress !== undefined) ride.preferredPickupAddress = preferredPickupAddress;
  if (preferredPickupLat !== undefined) ride.preferredPickupLat = preferredPickupLat;
  if (preferredPickupLng !== undefined) ride.preferredPickupLng = preferredPickupLng;
  if (preferredDropoffAddress !== undefined) ride.preferredDropoffAddress = preferredDropoffAddress;
  if (preferredDropoffLat !== undefined) ride.preferredDropoffLat = preferredDropoffLat;
  if (preferredDropoffLng !== undefined) ride.preferredDropoffLng = preferredDropoffLng;

  if (notes) ride.notes = notes;

  // Save updated ride
  await ride.save();

  res.status(200).json(ride);
});

// @desc    Delete ride
// @route   DELETE /api/riderequests/:id
// @access  Private
export const deleteRideRequest = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.id;
  const user = req.user;

  if (!user) {
    throw new AppError("User not found", 404);
  }

  // Find ride by ID
  const ride = await RideRequest.findByPk(rideId);

  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  // Check if user is the ride owner
  //   if (ride.driverId !== user.id && user.role !== "admin") {
  //     throw new AppError("Not authorized to delete this ride", 403);
  //   }

  // Delete ride
  await ride.destroy();

  res.status(200).json({ message: "Ride deleted successfully" });
});

// @desc    Get user's rides
// @route   GET /api/riderequests/user
// @access  Private
export const getUserRideRequests = asyncHandler(
  async (req: Request, res: Response) => {
    const user = req.user;

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Get user's rides
    const rides = await RideRequest.findAll({
      where: {
        userId: user.id,
        [Op.and]: {
          date: {
            [Op.gte]: new Date(),
          },
        },
      },
      order: [
        ["date", "ASC"],
        ["time", "ASC"],
      ],
    });

    res.status(200).json(rides);
  }
);

// Helper function to generate consistent avatar colors
function getAvatarColor(id: string): string {
  const colors = [
    "bg-desert-primary",
    "bg-desert-secondary",
    "bg-desert-accent",
  ];
  const index = id.charCodeAt(0) % colors.length;
  return colors[index];
}
