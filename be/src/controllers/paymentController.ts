import { Request, Response } from "express";
import User from "../models/userModel.js";
import RideModel from "../models/rideModel.js";
import Stripe from "stripe";

// const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-07-30.basil" });
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-07-30.basil" });
const STRIPE_ENABLED = process.env.STRIPE_ENABLED === 'true';

export const createPaymentIntent = async (req: Request, res: Response) => {
  if (!STRIPE_ENABLED) {
    return res.status(503).json({ error: "Stripe integration is temporarily disabled." });
  }
  try {
    const { rideId, seats = 1, suitcases = 1, currency = "usd" } = req.body;

    // Ride lookup in database
    const ride = await RideModel.findByPk(rideId);
    if (!ride) {
      return res.status(404).json({ error: "Ride not found" });
    }

    // Calculate the total amount (example: price per seat)
    const amount = ride.price * seats * 100; // Stripe expects cents

    // Create the PaymentIntent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency,
      capture_method: 'manual',
      automatic_payment_methods: { enabled: true },
      metadata: {
        rideId,
        seats,
        suitcases,
      },
    });

    res.json({ clientSecret: paymentIntent.client_secret });
  } catch (err) {
    // Log the error object and its properties
    console.error("PaymentIntent error:", err);
    if (err instanceof Error) {
      console.error("Error message:", err.message);
      console.error("Error stack:", err.stack);
    }
    res.status(500).json({ error: "Failed to create PaymentIntent" });
  }
};