// filepath: /Users/<USER>/kamel-webapp/be/controllers/reviewController.ts
import { Request, Response } from 'express';
import { asyncHandler, AppError } from '../middleware/errorMiddleware.js';
import { Booking } from '../models/bookingModel.js';
import { User } from '../models/userModel.js';
import { Review } from '../models/reviewModel.js';
import { Ride } from '../models/rideModel.js';
import { Op } from 'sequelize'

// @desc    Get reviews by userID
// @route   GET /api/reviews/getPassengerReviews/:userId
//Get Reviews Where Use is Passenger
export const getPendingPassengerReviews = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.params.userId;

  // Build local-date and local-time strings in "YYYY-MM-DD" and "HH:MM" formats
  const now = new Date();
  const pad = (n: number) => n.toString().padStart(2, '0');
  const today = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}`;
  const currentTime = `${pad(now.getHours())}:${pad(now.getMinutes())}`;

  // Fetch all bookings for this user where passengerHasReviewed is false
  // and the associated ride’s date/time is already past
  const bookings = await Booking.findAll({
    where: {
      userId,
      passengerHasReviewed: false,
    },
    include: [
      {
        model: Ride,
        as: 'ride',
        where: {
          [Op.or]: [
            // any ride date strictly before today
            { date: { [Op.lt]: today } },
            // rides today that started at or before current time
            {
              date: today,
              time: { [Op.lte]: currentTime },
            },
          ],
        },
        include: [
          {
            model: User,
            as: 'driver',
            attributes: ['id', 'name', 'avatar'],
          },
        ],
      },
    ],
  });

  res.json(bookings);
});



// @desc    Get reviews by userID
// @route   GET /api/reviews/getDriverReviews/:userId
export const getPendingDriverReviews = asyncHandler(async (req: Request, res: Response) => {
  const userId = req.params.userId;

  const now = new Date();
  const currentDate = now.toISOString().split('T')[0];
  const currentTime = now.toTimeString().slice(0, 5);

  try {
    // First, get rides for this driver that are in the past
    const pastRides = await Ride.findAll({
      where: {
        driverId: userId,
        [Op.or]: [
          { date: { [Op.lt]: currentDate } },
          {
            date: currentDate,
            time: { [Op.lt]: currentTime },
          },
        ],
      },
      attributes: ['id', 'from', 'to', 'date', 'time'],
    });

    if (pastRides.length === 0) {
      return res.json([]);
    }

    const rideIds = pastRides.map(ride => ride.id);

    // Then get bookings for these rides where driver hasn't reviewed
    const bookings = await Booking.findAll({
      where: {
        rideId: { [Op.in]: rideIds },
        driverHasReviewed: false,
      },
      attributes: ['id', 'userId', 'rideId'],
    });

    if (bookings.length === 0) {
      return res.json([]);
    }

    // Get user info for these bookings
    const userIds = bookings.map(booking => booking.userId);
    const users = await User.findAll({
      where: { id: { [Op.in]: userIds } },
      attributes: ['id', 'name', 'avatar'],
    });

    // Create a map for quick lookup
    const userMap = new Map(users.map(user => [user.id, user]));
    const rideMap = new Map(pastRides.map(ride => [ride.id, ride]));

    // Format the response
    const formattedBookings = bookings.map(booking => {
      const user = userMap.get(booking.userId);
      const ride = rideMap.get(booking.rideId);

      return {
        id: booking.id,
        ride: {
          id: ride?.id,
          from: ride?.from,
          to: ride?.to,
        },
        user: {
          id: user?.id,
          name: user?.name,
          avatar: user?.avatar,
        }
      };
    });

    res.json(formattedBookings);
  } catch (error) {
    console.error('Error fetching pending driver reviews:', error);
    throw new AppError('Failed to fetch pending driver reviews', 500);
  }
});

// Submit a review for a ride
export const submitReview = asyncHandler(async (req: Request, res: Response) => {
  const { bookingId, stars, comment, reviewType } = req.body; // reviewType: 'driver' (passenger reviews driver) or 'passenger' (driver reviews passenger)

  // Fetch the booking to get ride and user IDs
  const booking = await Booking.findByPk(bookingId);
  if (!booking) {
    throw new AppError("Booking not found", 404);
  }

  // Retrieve the ride to get the driver ID if needed
  const ride = await Ride.findByPk(booking.rideId);
  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  let reviewerId: string;
  let revieweeId: string;

  //for driver
  if (reviewType === 'driver') {
    if (booking.passengerHasReviewed) {
      throw new AppError("Driver review has already been submitted", 400);
    }
    reviewerId = booking.userId;    // passenger
    revieweeId = ride.driverId;       // driver
    booking.passengerHasReviewed = true;
    await booking.save();
    //for passenger
  } else if (reviewType === 'passenger') {
    if (booking.driverHasReviewed) {
      throw new AppError("Passenger review has already been submitted", 400);
    }
    reviewerId = ride.driverId;       // driver
    revieweeId = booking.userId;      // passenger
    booking.driverHasReviewed = true;
    await booking.save();
  } else {
    throw new AppError("Invalid review type", 400);
  }

  console.log({
    rideId: booking.rideId,
    bookingId: booking.id,
    reviewerId,
    revieweeId,
    stars,
    comment,
    reviewType,
  });

  // Create a new review record in the Reviews table
  const review = await Review.create({
    rideId: booking.rideId,
    bookingId: booking.id,
    reviewerId,
    revieweeId,
    stars,
    comment,
    reviewType,
  });

  res.status(201).json({ message: "Review submitted successfully", review });
});

// @desc    Get all reviews received by a user (as reviewee)
// @route   GET /api/:userId
// @access  Public 
export const getReviewsByReviewee = asyncHandler(async (req: Request, res: Response) => {
  const revieweeId = req.params.userId;
  if (!revieweeId || typeof revieweeId !== 'string') {
    return res.status(400).json({ error: 'revieweeId query parameter is required' });
  }

  // Fetch all reviews where revieweeId matches
  const reviews = await Review.findAll({
    where: { revieweeId: revieweeId },
    order: [['createdAt', 'DESC']],
    include: [
      {
        model: User,
        as: 'reviewer',
        attributes: ['id', 'name', 'avatar'],
      },
      {
        model: Ride,
        as: 'ride',
        attributes: ['id', 'from', 'to', 'date', 'time'],
      },
    ],
  });

  res.status(200).json(reviews);
});
