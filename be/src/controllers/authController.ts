import { Request, Response } from 'express';
import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';
import { User } from '../models/userModel.js';
import { asyncHandler, AppError } from '../middleware/errorMiddleware.js';
import { generateAuthResponse } from '../utils/jwtUtils.js';

// Initialize Firebase Admin
initializeApp({
  credential: cert({
    projectId: process.env.FIREBASE_PROJECT_ID,
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n')
  })
});

// @desc    Register a new user
// @route   POST /api/auth/register
// @access  Public
export const register = asyncHandler(async (req: Request, res: Response) => {
  const { name, email, password } = req.body;

  // Check if user already exists
  const userExists = await User.findOne({ where: { email } });

  if (userExists) {
    throw new AppError('User already exists', 400);
  }

  // Create new user
  const user = await User.create({
    name,
    email,
    password,
  });

  // Return user data with token
  res.status(201).json(generateAuthResponse(user));
});

// @desc    Login user
// @route   POST /api/auth/login
// @access  Public
export const login = asyncHandler(async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Find user by email
  const user = await User.findOne({ where: { email } });

  // Check if user exists and password matches
  if (!user || !(await user.matchPassword(password))) {
    throw new AppError('Invalid email or password', 401);
  }

  // Return user data with token
  res.status(200).json(generateAuthResponse(user));
});

// @desc    Login or register with Google
// @route   POST /api/auth/google
// @access  Public
export const googleAuth = asyncHandler(async (req: Request, res: Response) => {
  const { tokenId } = req.body;

  try {
    // Verify the Firebase ID token
    const decodedToken = await getAuth().verifyIdToken(tokenId);
    const { email, name, picture } = decodedToken;

    if (!email) {
      throw new AppError('Email not found in token', 400);
    }

    // Apply email domain restriction for Cornell region (ONLY if in production)
    const isDev = process.env.NODE_ENV !== 'production';

    // if (req.region === 'cornell' &&
    //   !isDev &&
    //   !/@(cornell\.edu|bloodandtreasure\.com|ithaca\.edu|binghamton\.edu|syr\.edu)$/i.test(email)) {
    //   return res.status(403).json({
    //     status: 'error',
    //     code: 'INVALID_EMAIL_DOMAIN',
    //     message: `${email} is not a valid email address. Please use your Cornell or Blood & Treasure email address.`
    //   });
    // }

    // Find user by email or create new user
    let user = await User.findOne({ where: { email } });

    if (!user) {
      user = await User.create({
        name: name || email?.split('@')[0],
        email,
        password: Math.random().toString(36).slice(-8) + Math.random().toString(36).toUpperCase().slice(-4),
        avatar: picture || null,
      });
    }

    res.status(200).json(generateAuthResponse(user));
  } catch (error) {
    console.error('Token verification error:', error);
    throw new AppError('Invalid token', 401);
  }
});

// @desc    Get current user profile
// @route   GET /api/auth/me
// @access  Private
export const getCurrentUser = asyncHandler(async (req: Request, res: Response) => {
  // User is already attached to request in auth middleware
  const user = req.user;

  if (!user) {
    throw new AppError('User not found', 404);
  }

  // Return user data with token
  res.status(200).json(generateAuthResponse(user));
});

// Verify token validity
export const verifyToken = asyncHandler(async (req: Request, res: Response) => {
  // If the request reaches here, it means the token is valid
  // (because it passed through the protect middleware)
  res.status(200).json({ valid: true });
});

