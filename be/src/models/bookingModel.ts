import { DataTypes, Optional } from 'sequelize';
import { BaseModel, addSchemaHook } from './baseModel.js';
import { sequelize } from '../config/database.js';
import { User } from './userModel.js';
import { Ride } from './rideModel.js';

// Booking status enum
export enum BookingStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
}

// Booking attributes interface
interface BookingAttributes {
  id: string;
  userId: string;
  rideId: string;
  seats: number;
  suitcases: number;
  status: BookingStatus;
  createdAt?: Date;
  updatedAt?: Date;
  driverHasReviewed: boolean;
  passengerHasReviewed: boolean;

  paymentIntentId: string;
  reminderSent?: boolean; // for booking expiry
  totalAmount: number;
  driverPaymentProcessed: boolean;
  driverPaymentDate?: Date;
  platformFeePercentage: number;
}

// Interface for Booking creation attributes
interface BookingCreationAttributes extends Optional<BookingAttributes, 'id'> { }

// Booking model class extending BaseModel
export class Booking extends BaseModel<BookingAttributes, BookingCreationAttributes> implements BookingAttributes {
  public id!: string;
  public userId!: string;
  public rideId!: string;
  public seats!: number;
  public suitcases!: number;
  public status!: BookingStatus;
  public driverHasReviewed!: boolean;
  public passengerHasReviewed!: boolean;

  public paymentIntentId!: string;
  public reminderSent?: boolean;
  public totalAmount!: number;
  public driverPaymentProcessed!: boolean;
  public driverPaymentDate?: Date;
  public platformFeePercentage!: number;

  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;
}

// Initialize Booking model
Booking.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    userId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    rideId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'rides',
        key: 'id',
      },
    },
    seats: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
      },
    },
    suitcases: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
      },
    },
    status: {
      type: DataTypes.ENUM(...Object.values(BookingStatus)),
      defaultValue: BookingStatus.PENDING,
      allowNull: false,
    },
    driverHasReviewed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    passengerHasReviewed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    paymentIntentId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    reminderSent: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    totalAmount: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'Total amount paid by passenger in cents (price * seats)',
    },
    driverPaymentProcessed: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    driverPaymentDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    platformFeePercentage: {
      type: DataTypes.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0.00,
    },
  },
  {
    sequelize: BaseModel.getSequelizeForRequest(),
    modelName: 'Booking',
    tableName: 'bookings',
    hooks: {
      // Update ride's available seats and suitcases after booking is created or updated
      afterSave: async (booking) => {
        if (booking.status === BookingStatus.CONFIRMED) {
          const ride = await Ride.findByPk(booking.rideId);
          if (ride) {
            ride.seatsAvailable = Math.max(0, ride.seatsAvailable - booking.seats);
            ride.suitcasesAvailable = Math.max(0, ride.suitcasesAvailable - booking.suitcases);
            await ride.save();
          }
        }
      },
      // Restore ride's available seats and suitcases after booking is cancelled
      afterUpdate: async (booking) => {
        if (booking.status === BookingStatus.CANCELLED && booking.previous('status') === BookingStatus.CONFIRMED) {
          const ride = await Ride.findByPk(booking.rideId);
          if (ride) {
            ride.seatsAvailable = Math.min(ride.totalSeats, ride.seatsAvailable + booking.seats);
            ride.suitcasesAvailable = Math.min(ride.totalSuitcases, ride.suitcasesAvailable + booking.suitcases);
            await ride.save();
          }
        }
      },
    },
  }
);

// Add schema hook instead of connection hooks
addSchemaHook(Booking);

// Define associations
Booking.belongsTo(User, {
  foreignKey: 'userId',
  as: 'user',
  constraints: false
});

Booking.belongsTo(Ride, {
  foreignKey: 'rideId',
  as: 'ride',
  constraints: false
});

User.hasMany(Booking, {
  foreignKey: 'userId',
  as: 'bookings',
  constraints: false
});

Ride.hasMany(Booking, {
  foreignKey: 'rideId',
  as: 'bookings',
  constraints: false
});

export default Booking;
