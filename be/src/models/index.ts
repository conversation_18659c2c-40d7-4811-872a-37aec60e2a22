import { BaseModel } from './baseModel.js';
import User from './userModel.js';
import Ride from './rideModel.js';
import Booking, { BookingStatus } from './bookingModel.js';
import Message from './messageModel.js';
import RideRequest from './rideRequestModel.js';
import Subscription from './subscriptionModel.js';

// Initialize all models and their relationships
const models = {
  User,
  Ride,
  Booking,
  Message,
  RideRequest,
  Subscription,
};

// Function to sync all models with the database
export const syncModels = async (force = false, region = 'cornell') => {
  try {
    // Get the correct sequelize instance for the region
    const sequelize = BaseModel.getSequelizeForRequest();
    await sequelize.sync({ force });
    console.log(`Database for region '${region}' synced successfully`);
  } catch (error) {
    console.error(`Error syncing database for region '${region}':`, error);
    throw error;
  }
};

export { User, Ride, Booking, BookingStatus, Message, RideRequest, Subscription };
export default models;
