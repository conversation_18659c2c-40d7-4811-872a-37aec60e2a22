'use strict';

/**
 * Dependencies
 */
const Personalization = require('../personalization');
const EmailAddress = require('../email-address');

/**
 * Tests
 */
describe('Personalization', function() {

  //Create new personalization before each test
  let p;
  beforeEach(function() {
    p = new Personalization();
  });

  //Set bcc
  describe('setBcc()', function() {
    it('should handle array values', function() {
      p.setBcc(['<EMAIL>']);
      expect(p.bcc).to.be.an.instanceof(Array);
      expect(p.bcc).to.have.a.lengthOf(1);
      expect(p.bcc[0]).to.be.an.instanceof(EmailAddress);
      expect(p.bcc[0].email).to.equal('<EMAIL>');
    });
    it('should handle string values', function() {
      p.setBcc('<EMAIL>');
      expect(p.bcc).to.be.an.instanceof(Array);
      expect(p.bcc).to.have.a.lengthOf(1);
      expect(p.bcc[0]).to.be.an.instanceof(EmailAddress);
      expect(p.bcc[0].email).to.equal('<EMAIL>');
    });
    it('should handle multiple values', function() {
      p.setBcc(['<EMAIL>', '<EMAIL>']);
      expect(p.bcc).to.be.an.instanceof(Array);
      expect(p.bcc).to.have.a.lengthOf(2);
      expect(p.bcc[0]).to.be.an.instanceof(EmailAddress);
      expect(p.bcc[0].email).to.equal('<EMAIL>');
      expect(p.bcc[1]).to.be.an.instanceof(EmailAddress);
      expect(p.bcc[1].email).to.equal('<EMAIL>');
    });
    it('should accept no input', function() {
      expect(function() {
        p.setBcc();
      }).not.to.throw(Error);
    });
    it('should not overwrite with no input', function() {
      p.setBcc('<EMAIL>');
      p.setBcc();
      expect(p.bcc[0].email).to.equal('<EMAIL>');
    });
  });
});
