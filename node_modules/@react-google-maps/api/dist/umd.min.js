!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react/jsx-runtime"),require("react"),require("react-dom")):"function"==typeof define&&define.amd?define(["exports","react/jsx-runtime","react","react-dom"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).reactGoogleMapsApi={},e.ReactJSXRuntime,e.React,e.ReactDOM)}(this,(function(e,t,n,s){"use strict";function o(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var s=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,s.get?s:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var r,i,a=o(s);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function u(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t||"default");if("object"!=l(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}function p(e,t,n){return(t=u(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var h=c(function(){if(i)return r;i=1;var e=process.env.NODE_ENV;return r=function(t,n,s,o,r,i,a,l){if("production"!==e&&void 0===n)throw new Error("invariant requires an error message argument");if(!t){var u;if(void 0===n)u=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var p=[s,o,r,i,a,l],c=0;(u=new Error(n.replace(/%s/g,(function(){return p[c++]})))).name="Invariant Violation"}throw u.framesToPop=1,u}}}()),d=n.createContext(null);function g(){h(!!n.useContext,"useGoogleMap is React hook and requires React version 16.8+");var e=n.useContext(d);return h(!!e,"useGoogleMap needs a GoogleMap available up in the tree"),e}function m(e,t,n,s){var o,r,i={};return o=e,r=(e,o)=>{var r=n[o];r!==t[o]&&(i[o]=r,e(s,r))},Object.keys(o).forEach((e=>r(o[e],e))),i}function v(e,t,n){var s,o,r,i=(s=n,o=function(n,s,o){return"function"==typeof e[o]&&n.push(google.maps.event.addListener(t,s,e[o])),n},r=[],Object.keys(s).reduce((function(e,t){return o(e,s[t],t)}),r));return i}function f(e){google.maps.event.removeListener(e)}function y(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(f)}function b(e){var{updaterMap:t,eventMap:n,prevProps:s,nextProps:o,instance:r}=e,i=v(o,r,n);return m(t,s,o,r),i}var L={onDblClick:"dblclick",onDragEnd:"dragend",onDragStart:"dragstart",onMapTypeIdChanged:"maptypeid_changed",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseDown:"mousedown",onMouseUp:"mouseup",onRightClick:"rightclick",onTilesLoaded:"tilesloaded",onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onDrag:"drag",onHeadingChanged:"heading_changed",onIdle:"idle",onProjectionChanged:"projection_changed",onResize:"resize",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed"},C={extraMapTypes(e,t){t.forEach((function(t,n){e.mapTypes.set(String(n),t)}))},center(e,t){e.setCenter(t)},clickableIcons(e,t){e.setClickableIcons(t)},heading(e,t){e.setHeading(t)},mapTypeId(e,t){e.setMapTypeId(t)},options(e,t){e.setOptions(t)},streetView(e,t){e.setStreetView(t)},tilt(e,t){e.setTilt(t)},zoom(e,t){e.setZoom(t)}};n.memo((function(e){var{children:s,options:o,id:r,mapContainerStyle:i,mapContainerClassName:a,center:l,onClick:u,onDblClick:p,onDrag:c,onDragEnd:h,onDragStart:g,onMouseMove:m,onMouseOut:v,onMouseOver:f,onMouseDown:y,onMouseUp:b,onRightClick:L,onCenterChanged:C,onLoad:E,onUnmount:M}=e,[w,x]=n.useState(null),k=n.useRef(null),[P,S]=n.useState(null),[O,D]=n.useState(null),[j,I]=n.useState(null),[B,T]=n.useState(null),[_,R]=n.useState(null),[U,z]=n.useState(null),[A,Z]=n.useState(null),[V,W]=n.useState(null),[N,H]=n.useState(null),[F,G]=n.useState(null),[K,Y]=n.useState(null),[q,J]=n.useState(null);return n.useEffect((()=>{o&&null!==w&&w.setOptions(o)}),[w,o]),n.useEffect((()=>{null!==w&&void 0!==l&&w.setCenter(l)}),[w,l]),n.useEffect((()=>{w&&p&&(null!==O&&google.maps.event.removeListener(O),D(google.maps.event.addListener(w,"dblclick",p)))}),[p]),n.useEffect((()=>{w&&h&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(w,"dragend",h)))}),[h]),n.useEffect((()=>{w&&g&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(w,"dragstart",g)))}),[g]),n.useEffect((()=>{w&&y&&(null!==_&&google.maps.event.removeListener(_),R(google.maps.event.addListener(w,"mousedown",y)))}),[y]),n.useEffect((()=>{w&&m&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(w,"mousemove",m)))}),[m]),n.useEffect((()=>{w&&v&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(w,"mouseout",v)))}),[v]),n.useEffect((()=>{w&&f&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(w,"mouseover",f)))}),[f]),n.useEffect((()=>{w&&b&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(w,"mouseup",b)))}),[b]),n.useEffect((()=>{w&&L&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(w,"rightclick",L)))}),[L]),n.useEffect((()=>{w&&u&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(w,"click",u)))}),[u]),n.useEffect((()=>{w&&c&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(w,"drag",c)))}),[c]),n.useEffect((()=>{w&&C&&(null!==P&&google.maps.event.removeListener(P),S(google.maps.event.addListener(w,"center_changed",C)))}),[u]),n.useEffect((()=>{var e=null===k.current?null:new google.maps.Map(k.current,o);return x(e),null!==e&&E&&E(e),()=>{null!==e&&M&&M(e)}}),[]),t.jsx("div",{id:r,ref:k,style:i,className:a,children:t.jsx(d.Provider,{value:w,children:null!==w?s:null})})}));class E extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{map:null}),p(this,"registeredEvents",[]),p(this,"mapRef",null),p(this,"getInstance",(()=>null===this.mapRef?null:new google.maps.Map(this.mapRef,this.props.options))),p(this,"panTo",(e=>{var t=this.getInstance();t&&t.panTo(e)})),p(this,"setMapCallback",(()=>{null!==this.state.map&&this.props.onLoad&&this.props.onLoad(this.state.map)})),p(this,"getRef",(e=>{this.mapRef=e}))}componentDidMount(){var e=this.getInstance();this.registeredEvents=b({updaterMap:C,eventMap:L,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{map:e}}),this.setMapCallback)}componentDidUpdate(e){null!==this.state.map&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:C,eventMap:L,prevProps:e,nextProps:this.props,instance:this.state.map}))}componentWillUnmount(){null!==this.state.map&&(this.props.onUnmount&&this.props.onUnmount(this.state.map),y(this.registeredEvents))}render(){return t.jsx("div",{id:this.props.id,ref:this.getRef,style:this.props.mapContainerStyle,className:this.props.mapContainerClassName,children:t.jsx(d.Provider,{value:this.state.map,children:null!==this.state.map?this.props.children:null})})}}function M(e,t,n,s,o,r,i){try{var a=e[r](i),l=a.value}catch(e){return void n(e)}a.done?t(l):Promise.resolve(l).then(s,o)}function w(e){return function(){var t=this,n=arguments;return new Promise((function(s,o){var r=e.apply(t,n);function i(e){M(r,s,o,i,a,"next",e)}function a(e){M(r,s,o,i,a,"throw",e)}i(void 0)}))}}function x(e){var{googleMapsApiKey:t,googleMapsClientId:n,version:s="weekly",language:o,region:r,libraries:i,channel:a,mapIds:l,authReferrerPolicy:u,apiUrl:p="https://maps.googleapis.com"}=e,c=[];return h(t&&n||!(t&&n),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),t?c.push("key=".concat(t)):n&&c.push("client=".concat(n)),s&&c.push("v=".concat(s)),o&&c.push("language=".concat(o)),r&&c.push("region=".concat(r)),i&&i.length&&c.push("libraries=".concat(i.sort().join(","))),a&&c.push("channel=".concat(a)),l&&l.length&&c.push("map_ids=".concat(l.join(","))),u&&c.push("auth_referrer_policy=".concat(u)),c.push("loading=async"),c.push("callback=initMap"),"".concat(p,"/maps/api/js?").concat(c.join("&"))}var k="undefined"!=typeof document;function P(e){var{url:t,id:n,nonce:s}=e;return k?new Promise((function(e,o){var r=document.getElementById(n),i=window;if(r){var a=r.getAttribute("data-state");if(r.src===t&&"error"!==a){if("ready"===a)return e(n);var l=i.initMap,u=r.onerror;return i.initMap=function(){l&&l(),e(n)},void(r.onerror=function(e){u&&u(e),o(e)})}r.remove()}var p=document.createElement("script");p.type="text/javascript",p.src=t,p.id=n,p.async=!0,p.nonce=s||"",p.onerror=function(e){p.setAttribute("data-state","error"),o(e)},i.initMap=function(){p.setAttribute("data-state","ready"),e(n)},document.head.appendChild(p)})).catch((e=>{throw console.error("injectScript error: ",e),e})):Promise.reject(new Error("document is undefined"))}function S(e){var t=e.href;return!(!t||0!==t.indexOf("https://fonts.googleapis.com/css?family=Roboto")&&0!==t.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text"))||("style"===e.tagName.toLowerCase()&&e.styleSheet&&e.styleSheet.cssText&&0===e.styleSheet.cssText.replace("\r\n","").indexOf(".gm-style")?(e.styleSheet.cssText="",!0):"style"===e.tagName.toLowerCase()&&e.innerHTML&&0===e.innerHTML.replace("\r\n","").indexOf(".gm-style")?(e.innerHTML="",!0):"style"===e.tagName.toLowerCase()&&!e.styleSheet&&!e.innerHTML)}function O(){var e=document.getElementsByTagName("head")[0];if(e){var t=e.insertBefore.bind(e);e.insertBefore=function(n,s){return S(n)||Reflect.apply(t,e,[n,s]),n};var n=e.appendChild.bind(e);e.appendChild=function(t){return S(t)||Reflect.apply(n,e,[t]),t}}}var D=!1;function j(){return t.jsx("div",{children:"Loading..."})}var I,B={id:"script-loader",version:"weekly"};class T extends n.PureComponent{constructor(){super(...arguments),p(this,"check",null),p(this,"state",{loaded:!1}),p(this,"cleanupCallback",(()=>{delete window.google.maps,this.injectScript()})),p(this,"isCleaningUp",w((function*(){return new Promise((function(e){if(D){if(k)var t=window.setInterval((function(){D||(window.clearInterval(t),e())}),1)}else e()}))}))),p(this,"cleanup",(()=>{D=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter((function(e){return"string"==typeof e.src&&e.src.includes("maps.googleapis")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("link")).filter((function(e){return"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"===e.href})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("style")).filter((function(e){return void 0!==e.innerText&&e.innerText.length>0&&e.innerText.includes(".gm-")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)}))})),p(this,"injectScript",(()=>{this.props.preventGoogleFontsLoading&&O(),h(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id),P({id:this.props.id,nonce:this.props.nonce,url:x(this.props)}).then((()=>{this.props.onLoad&&this.props.onLoad(),this.setState((function(){return{loaded:!0}}))})).catch((e=>{this.props.onError&&this.props.onError(e),console.error("\n          There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(this.props.googleMapsApiKey||"-",") or Client ID (").concat(this.props.googleMapsClientId||"-",") to <LoadScript />\n          Otherwise it is a Network issue.\n        "))}))})),p(this,"getRef",(e=>{this.check=e}))}componentDidMount(){if(k){if(window.google&&window.google.maps&&!D)return void console.error("google api is already presented");this.isCleaningUp().then(this.injectScript).catch((function(e){console.error("Error at injecting script after cleaning up: ",e)}))}}componentDidUpdate(e){this.props.libraries!==e.libraries&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),k&&e.language!==this.props.language&&(this.cleanup(),this.setState((function(){return{loaded:!1}}),this.cleanupCallback))}componentWillUnmount(){if(k){this.cleanup();window.setTimeout((()=>{this.check||(delete window.google,D=!1)}),1),this.props.onUnmount&&this.props.onUnmount()}}render(){return t.jsxs(t.Fragment,{children:[t.jsx("div",{ref:this.getRef}),this.state.loaded?this.props.children:this.props.loadingElement||t.jsx(j,{})]})}}function _(e,t){if(null==e)return{};var n,s,o=function(e,t){if(null==e)return{};var n={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(t.includes(s))continue;n[s]=e[s]}return n}(e,t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);for(s=0;s<r.length;s++)n=r[s],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function R(e){var{id:t=B.id,version:s=B.version,nonce:o,googleMapsApiKey:r,googleMapsClientId:i,language:a,region:l,libraries:u,preventGoogleFontsLoading:p,channel:c,mapIds:d,authReferrerPolicy:g,apiUrl:m="https://maps.googleapis.com"}=e,v=n.useRef(!1),[f,y]=n.useState(!1),[b,L]=n.useState(void 0);n.useEffect((function(){return v.current=!0,()=>{v.current=!1}}),[]),n.useEffect((function(){k&&p&&O()}),[p]),n.useEffect((function(){f&&h(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")}),[f]);var C=x({version:s,googleMapsApiKey:r,googleMapsClientId:i,language:a,region:l,libraries:u,channel:c,mapIds:d,authReferrerPolicy:g,apiUrl:m});n.useEffect((function(){function e(){v.current&&(y(!0),I=C)}k&&(window.google&&window.google.maps&&I===C?e():P({id:t,url:C,nonce:o}).then(e).catch((function(e){v.current&&L(e),console.warn("\n        There has been an Error with loading Google Maps API script, please check that you provided correct google API key (".concat(r||"-",") or Client ID (").concat(i||"-",")\n        Otherwise it is a Network issue.\n      ")),console.error(e)})))}),[t,C,o]);var E=n.useRef(void 0);return n.useEffect((function(){E.current&&u!==E.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),E.current=u}),[u]),{isLoaded:f,loadError:b,url:C}}p(T,"defaultProps",B);var U=["loadingElement","onLoad","onError","onUnmount","children"],z=t.jsx(j,{});var A=n.memo((function(e){var{loadingElement:t,onLoad:s,onError:o,onUnmount:r,children:i}=e,a=_(e,U),{isLoaded:l,loadError:u}=R(a);return n.useEffect((function(){l&&"function"==typeof s&&s()}),[l,s]),n.useEffect((function(){u&&"function"==typeof o&&o(u)}),[u,o]),n.useEffect((function(){return()=>{r&&r()}}),[r]),l?i:t||z}));function Z(e,t,n,s){return new(n||(n=Promise))((function(t,o){function r(e){try{a(s.next(e))}catch(e){o(e)}}function i(e){try{a(s.throw(e))}catch(e){o(e)}}function a(e){var s;e.done?t(e.value):(s=e.value,s instanceof n?s:new n((function(e){e(s)}))).then(r,i)}a((s=s.apply(e,[])).next())}))}function V(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}"function"==typeof SuppressedError&&SuppressedError;var W,N=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var s,o,r;if(Array.isArray(t)){if((s=t.length)!=n.length)return!1;for(o=s;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((s=(r=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=s;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,r[o]))return!1;for(o=s;0!=o--;){var i=r[o];if(!e(t[i],n[i]))return!1}return!0}return t!=t&&n!=n},H=V(N),F="__googleMapsScriptId";!function(e){e[e.INITIALIZED=0]="INITIALIZED",e[e.LOADING=1]="LOADING",e[e.SUCCESS=2]="SUCCESS",e[e.FAILURE=3]="FAILURE"}(W||(W={}));class G{constructor(e){var{apiKey:t,authReferrerPolicy:n,channel:s,client:o,id:r=F,language:i,libraries:a=[],mapIds:l,nonce:u,region:p,retries:c=3,url:h="https://maps.googleapis.com/maps/api/js",version:d}=e;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=n,this.channel=s,this.client=o,this.id=r||F,this.language=i,this.libraries=a,this.mapIds=l,this.nonce=u,this.region=p,this.retries=c,this.url=h,this.version=d,G.instance){if(!H(this.options,G.instance.options))throw new Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options)," !== ").concat(JSON.stringify(G.instance.options)));return G.instance}G.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?W.FAILURE:this.done?W.SUCCESS:this.loading?W.LOADING:W.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){var e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+="&key=".concat(this.apiKey)),this.channel&&(e+="&channel=".concat(this.channel)),this.client&&(e+="&client=".concat(this.client)),this.libraries.length>0&&(e+="&libraries=".concat(this.libraries.join(","))),this.language&&(e+="&language=".concat(this.language)),this.region&&(e+="&region=".concat(this.region)),this.version&&(e+="&v=".concat(this.version)),this.mapIds&&(e+="&map_ids=".concat(this.mapIds.join(","))),this.authReferrerPolicy&&(e+="&auth_referrer_policy=".concat(this.authReferrerPolicy)),e}deleteScript(){var e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise(((e,t)=>{this.loadCallback((n=>{n?t(n.error):e(window.google)}))}))}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){var e,t;if(document.getElementById(this.id))this.callback();else{var n={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(n).forEach((e=>!n[e]&&delete n[e])),(null===(t=null===(e=null===window||void 0===window?void 0:window.google)||void 0===e?void 0:e.maps)||void 0===t?void 0:t.importLibrary)||(e=>{var t,n,s,o="The Google Maps JavaScript API",r="google",i="importLibrary",a="__ib__",l=document,u=window,p=(u=u[r]||(u[r]={})).maps||(u.maps={}),c=new Set,h=new URLSearchParams,d=()=>t||(t=new Promise(((i,u)=>Z(this,0,void 0,(function*(){var d;for(s in yield n=l.createElement("script"),n.id=this.id,h.set("libraries",[...c]+""),e)h.set(s.replace(/[A-Z]/g,(e=>"_"+e[0].toLowerCase())),e[s]);h.set("callback",r+".maps."+a),n.src=this.url+"?"+h,p[a]=i,n.onerror=()=>t=u(Error(o+" could not load.")),n.nonce=this.nonce||(null===(d=l.querySelector("script[nonce]"))||void 0===d?void 0:d.nonce)||"",l.head.append(n)})))));p[i]?console.warn(o+" only loads once. Ignoring:",e):p[i]=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return c.add(e)&&d().then((()=>p[i](e,...n)))}})(n);var s=this.libraries.map((e=>this.importLibrary(e)));s.length||s.push(this.importLibrary("core")),Promise.all(s).then((()=>this.callback()),(e=>{var t=new ErrorEvent("error",{error:e});this.loadErrorCallback(t)}))}}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){var t=this.errors.length*Math.pow(2,this.errors.length);console.error("Failed to load Google Maps script, retrying in ".concat(t," ms.")),setTimeout((()=>{this.deleteScript(),this.setScript()}),t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach((e=>{e(this.onerrorEvent)})),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version)return console.warn("Google Maps already loaded outside @googlemaps/js-api-loader. This may result in undesirable behavior as options and script parameters may not match."),void this.callback();this.loading=!0,this.setScript()}}}var K=["maps"];function Y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Y(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Y(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var J={},X={options(e,t){e.setOptions(t)}};var $=n.memo((function(e){var{options:t,onLoad:s,onUnmount:o}=e,r=n.useContext(d),[i,a]=n.useState(null);return n.useEffect((()=>{null!==i&&i.setMap(r)}),[r]),n.useEffect((()=>{t&&null!==i&&i.setOptions(t)}),[i,t]),n.useEffect((()=>{var e=new google.maps.TrafficLayer(q(q({},t),{},{map:r}));return a(e),s&&s(e),()=>{null!==i&&(o&&o(i),i.setMap(null))}}),[]),null}));class Q extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{trafficLayer:null}),p(this,"setTrafficLayerCallback",(()=>{null!==this.state.trafficLayer&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)})),p(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(q(q({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:X,eventMap:J,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{trafficLayer:e}}),this.setTrafficLayerCallback)}componentDidUpdate(e){null!==this.state.trafficLayer&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:X,eventMap:J,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){null!==this.state.trafficLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),y(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}}p(Q,"contextType",d);var ee=n.memo((function(e){var{onLoad:t,onUnmount:s}=e,o=n.useContext(d),[r,i]=n.useState(null);return n.useEffect((()=>{null!==r&&r.setMap(o)}),[o]),n.useEffect((()=>{var e=new google.maps.BicyclingLayer;return i(e),e.setMap(o),t&&t(e),()=>{null!==e&&(s&&s(e),e.setMap(null))}}),[]),null}));class te extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{bicyclingLayer:null}),p(this,"setBicyclingLayerCallback",(()=>{null!==this.state.bicyclingLayer&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))}))}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState((()=>({bicyclingLayer:e})),this.setBicyclingLayerCallback)}componentWillUnmount(){null!==this.state.bicyclingLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}}p(te,"contextType",d);var ne=n.memo((function(e){var{onLoad:t,onUnmount:s}=e,o=n.useContext(d),[r,i]=n.useState(null);return n.useEffect((()=>{null!==r&&r.setMap(o)}),[o]),n.useEffect((()=>{var e=new google.maps.TransitLayer;return i(e),e.setMap(o),t&&t(e),()=>{null!==r&&(s&&s(r),r.setMap(null))}}),[]),null}));class se extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{transitLayer:null}),p(this,"setTransitLayerCallback",(()=>{null!==this.state.transitLayer&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))}))}componentDidMount(){var e=new google.maps.TransitLayer;this.setState((function(){return{transitLayer:e}}),this.setTransitLayerCallback)}componentWillUnmount(){null!==this.state.transitLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}}function oe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function re(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?oe(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):oe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(se,"contextType",d);var ie={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},ae={drawingMode(e,t){e.setDrawingMode(t)},options(e,t){e.setOptions(t)}};var le=n.memo((function(e){var{options:t,drawingMode:s,onCircleComplete:o,onMarkerComplete:r,onOverlayComplete:i,onPolygonComplete:a,onPolylineComplete:l,onRectangleComplete:u,onLoad:p,onUnmount:c}=e,g=n.useContext(d),[m,v]=n.useState(null),[f,y]=n.useState(null),[b,L]=n.useState(null),[C,E]=n.useState(null),[M,w]=n.useState(null),[x,k]=n.useState(null),[P,S]=n.useState(null);return n.useEffect((()=>{null!==m&&m.setMap(g)}),[g]),n.useEffect((()=>{t&&null!==m&&m.setOptions(t)}),[m,t]),n.useEffect((()=>{null!==m&&m.setDrawingMode(null!=s?s:null)}),[m,s]),n.useEffect((()=>{m&&o&&(null!==f&&google.maps.event.removeListener(f),y(google.maps.event.addListener(m,"circlecomplete",o)))}),[m,o]),n.useEffect((()=>{m&&r&&(null!==b&&google.maps.event.removeListener(b),L(google.maps.event.addListener(m,"markercomplete",r)))}),[m,r]),n.useEffect((()=>{m&&i&&(null!==C&&google.maps.event.removeListener(C),E(google.maps.event.addListener(m,"overlaycomplete",i)))}),[m,i]),n.useEffect((()=>{m&&a&&(null!==M&&google.maps.event.removeListener(M),w(google.maps.event.addListener(m,"polygoncomplete",a)))}),[m,a]),n.useEffect((()=>{m&&l&&(null!==x&&google.maps.event.removeListener(x),k(google.maps.event.addListener(m,"polylinecomplete",l)))}),[m,l]),n.useEffect((()=>{m&&u&&(null!==P&&google.maps.event.removeListener(P),S(google.maps.event.addListener(m,"rectanglecomplete",u)))}),[m,u]),n.useEffect((()=>{h(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var e=new google.maps.drawing.DrawingManager(re(re({},t),{},{map:g}));return s&&e.setDrawingMode(s),o&&y(google.maps.event.addListener(e,"circlecomplete",o)),r&&L(google.maps.event.addListener(e,"markercomplete",r)),i&&E(google.maps.event.addListener(e,"overlaycomplete",i)),a&&w(google.maps.event.addListener(e,"polygoncomplete",a)),l&&k(google.maps.event.addListener(e,"polylinecomplete",l)),u&&S(google.maps.event.addListener(e,"rectanglecomplete",u)),v(e),p&&p(e),()=>{null!==m&&(f&&google.maps.event.removeListener(f),b&&google.maps.event.removeListener(b),C&&google.maps.event.removeListener(C),M&&google.maps.event.removeListener(M),x&&google.maps.event.removeListener(x),P&&google.maps.event.removeListener(P),c&&c(m),m.setMap(null))}}),[]),null}));class ue extends n.PureComponent{constructor(e){super(e),p(this,"registeredEvents",[]),p(this,"state",{drawingManager:null}),p(this,"setDrawingManagerCallback",(()=>{null!==this.state.drawingManager&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)})),h(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(re(re({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:ae,eventMap:ie,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{drawingManager:e}}),this.setDrawingManagerCallback)}componentDidUpdate(e){null!==this.state.drawingManager&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:ae,eventMap:ie,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){null!==this.state.drawingManager&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),y(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}}function pe(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function ce(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pe(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pe(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(ue,"contextType",d);var he={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},de={animation(e,t){e.setAnimation(t)},clickable(e,t){e.setClickable(t)},cursor(e,t){e.setCursor(t)},draggable(e,t){e.setDraggable(t)},icon(e,t){e.setIcon(t)},label(e,t){e.setLabel(t)},map(e,t){e.setMap(t)},opacity(e,t){e.setOpacity(t)},options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},shape(e,t){e.setShape(t)},title(e,t){e.setTitle(t)},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},ge={};var me=n.memo((function(e){var{position:s,options:o,clusterer:r,noClustererRedraw:i,children:a,draggable:l,visible:u,animation:p,clickable:c,cursor:h,icon:g,label:m,opacity:v,shape:f,title:y,zIndex:b,onClick:L,onDblClick:C,onDrag:E,onDragEnd:M,onDragStart:w,onMouseOut:x,onMouseOver:k,onMouseUp:P,onMouseDown:S,onRightClick:O,onClickableChanged:D,onCursorChanged:j,onAnimationChanged:I,onDraggableChanged:B,onFlatChanged:T,onIconChanged:_,onPositionChanged:R,onShapeChanged:U,onTitleChanged:z,onVisibleChanged:A,onZindexChanged:Z,onLoad:V,onUnmount:W}=e,N=n.useContext(d),[H,F]=n.useState(null),[G,K]=n.useState(null),[Y,q]=n.useState(null),[J,X]=n.useState(null),[$,Q]=n.useState(null),[ee,te]=n.useState(null),[ne,se]=n.useState(null),[oe,re]=n.useState(null),[ie,ae]=n.useState(null),[le,ue]=n.useState(null),[pe,he]=n.useState(null),[de,me]=n.useState(null),[ve,fe]=n.useState(null),[ye,be]=n.useState(null),[Le,Ce]=n.useState(null),[Ee,Me]=n.useState(null),[we,xe]=n.useState(null),[ke,Pe]=n.useState(null),[Se,Oe]=n.useState(null),[De,je]=n.useState(null),[Ie,Be]=n.useState(null),[Te,_e]=n.useState(null);n.useEffect((()=>{null!==H&&H.setMap(N)}),[N]),n.useEffect((()=>{void 0!==o&&null!==H&&H.setOptions(o)}),[H,o]),n.useEffect((()=>{void 0!==l&&null!==H&&H.setDraggable(l)}),[H,l]),n.useEffect((()=>{s&&null!==H&&H.setPosition(s)}),[H,s]),n.useEffect((()=>{void 0!==u&&null!==H&&H.setVisible(u)}),[H,u]),n.useEffect((()=>{null==H||H.setAnimation(p)}),[H,p]),n.useEffect((()=>{H&&void 0!==c&&H.setClickable(c)}),[H,c]),n.useEffect((()=>{H&&void 0!==h&&H.setCursor(h)}),[H,h]),n.useEffect((()=>{H&&void 0!==g&&H.setIcon(g)}),[H,g]),n.useEffect((()=>{H&&void 0!==m&&H.setLabel(m)}),[H,m]),n.useEffect((()=>{H&&void 0!==v&&H.setOpacity(v)}),[H,v]),n.useEffect((()=>{H&&void 0!==f&&H.setShape(f)}),[H,f]),n.useEffect((()=>{H&&void 0!==y&&H.setTitle(y)}),[H,y]),n.useEffect((()=>{H&&void 0!==b&&H.setZIndex(b)}),[H,b]),n.useEffect((()=>{H&&C&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(H,"dblclick",C)))}),[C]),n.useEffect((()=>{H&&M&&(null!==Y&&google.maps.event.removeListener(Y),q(google.maps.event.addListener(H,"dragend",M)))}),[M]),n.useEffect((()=>{H&&w&&(null!==J&&google.maps.event.removeListener(J),X(google.maps.event.addListener(H,"dragstart",w)))}),[w]),n.useEffect((()=>{H&&S&&(null!==$&&google.maps.event.removeListener($),Q(google.maps.event.addListener(H,"mousedown",S)))}),[S]),n.useEffect((()=>{H&&x&&(null!==ee&&google.maps.event.removeListener(ee),te(google.maps.event.addListener(H,"mouseout",x)))}),[x]),n.useEffect((()=>{H&&k&&(null!==ne&&google.maps.event.removeListener(ne),se(google.maps.event.addListener(H,"mouseover",k)))}),[k]),n.useEffect((()=>{H&&P&&(null!==oe&&google.maps.event.removeListener(oe),re(google.maps.event.addListener(H,"mouseup",P)))}),[P]),n.useEffect((()=>{H&&O&&(null!==ie&&google.maps.event.removeListener(ie),ae(google.maps.event.addListener(H,"rightclick",O)))}),[O]),n.useEffect((()=>{H&&L&&(null!==le&&google.maps.event.removeListener(le),ue(google.maps.event.addListener(H,"click",L)))}),[L]),n.useEffect((()=>{H&&E&&(null!==pe&&google.maps.event.removeListener(pe),he(google.maps.event.addListener(H,"drag",E)))}),[E]),n.useEffect((()=>{H&&D&&(null!==de&&google.maps.event.removeListener(de),me(google.maps.event.addListener(H,"clickable_changed",D)))}),[D]),n.useEffect((()=>{H&&j&&(null!==ve&&google.maps.event.removeListener(ve),fe(google.maps.event.addListener(H,"cursor_changed",j)))}),[j]),n.useEffect((()=>{H&&I&&(null!==ye&&google.maps.event.removeListener(ye),be(google.maps.event.addListener(H,"animation_changed",I)))}),[I]),n.useEffect((()=>{H&&B&&(null!==Le&&google.maps.event.removeListener(Le),Ce(google.maps.event.addListener(H,"draggable_changed",B)))}),[B]),n.useEffect((()=>{H&&T&&(null!==Ee&&google.maps.event.removeListener(Ee),Me(google.maps.event.addListener(H,"flat_changed",T)))}),[T]),n.useEffect((()=>{H&&_&&(null!==we&&google.maps.event.removeListener(we),xe(google.maps.event.addListener(H,"icon_changed",_)))}),[_]),n.useEffect((()=>{H&&R&&(null!==ke&&google.maps.event.removeListener(ke),Pe(google.maps.event.addListener(H,"position_changed",R)))}),[R]),n.useEffect((()=>{H&&U&&(null!==Se&&google.maps.event.removeListener(Se),Oe(google.maps.event.addListener(H,"shape_changed",U)))}),[U]),n.useEffect((()=>{H&&z&&(null!==De&&google.maps.event.removeListener(De),je(google.maps.event.addListener(H,"title_changed",z)))}),[z]),n.useEffect((()=>{H&&A&&(null!==Ie&&google.maps.event.removeListener(Ie),Be(google.maps.event.addListener(H,"visible_changed",A)))}),[A]),n.useEffect((()=>{H&&Z&&(null!==Te&&google.maps.event.removeListener(Te),_e(google.maps.event.addListener(H,"zindex_changed",Z)))}),[Z]),n.useEffect((()=>{var e=ce(ce(ce({},o||ge),r?ge:{map:N}),{},{position:s}),t=new google.maps.Marker(e);return r?r.addMarker(t,!!i):t.setMap(N),s&&t.setPosition(s),void 0!==u&&t.setVisible(u),void 0!==l&&t.setDraggable(l),void 0!==c&&t.setClickable(c),"string"==typeof h&&t.setCursor(h),g&&t.setIcon(g),void 0!==m&&t.setLabel(m),void 0!==v&&t.setOpacity(v),f&&t.setShape(f),"string"==typeof y&&t.setTitle(y),"number"==typeof b&&t.setZIndex(b),C&&K(google.maps.event.addListener(t,"dblclick",C)),M&&q(google.maps.event.addListener(t,"dragend",M)),w&&X(google.maps.event.addListener(t,"dragstart",w)),S&&Q(google.maps.event.addListener(t,"mousedown",S)),x&&te(google.maps.event.addListener(t,"mouseout",x)),k&&se(google.maps.event.addListener(t,"mouseover",k)),P&&re(google.maps.event.addListener(t,"mouseup",P)),O&&ae(google.maps.event.addListener(t,"rightclick",O)),L&&ue(google.maps.event.addListener(t,"click",L)),E&&he(google.maps.event.addListener(t,"drag",E)),D&&me(google.maps.event.addListener(t,"clickable_changed",D)),j&&fe(google.maps.event.addListener(t,"cursor_changed",j)),I&&be(google.maps.event.addListener(t,"animation_changed",I)),B&&Ce(google.maps.event.addListener(t,"draggable_changed",B)),T&&Me(google.maps.event.addListener(t,"flat_changed",T)),_&&xe(google.maps.event.addListener(t,"icon_changed",_)),R&&Pe(google.maps.event.addListener(t,"position_changed",R)),U&&Oe(google.maps.event.addListener(t,"shape_changed",U)),z&&je(google.maps.event.addListener(t,"title_changed",z)),A&&Be(google.maps.event.addListener(t,"visible_changed",A)),Z&&_e(google.maps.event.addListener(t,"zindex_changed",Z)),F(t),V&&V(t),()=>{null!==G&&google.maps.event.removeListener(G),null!==Y&&google.maps.event.removeListener(Y),null!==J&&google.maps.event.removeListener(J),null!==$&&google.maps.event.removeListener($),null!==ee&&google.maps.event.removeListener(ee),null!==ne&&google.maps.event.removeListener(ne),null!==oe&&google.maps.event.removeListener(oe),null!==ie&&google.maps.event.removeListener(ie),null!==le&&google.maps.event.removeListener(le),null!==de&&google.maps.event.removeListener(de),null!==ve&&google.maps.event.removeListener(ve),null!==ye&&google.maps.event.removeListener(ye),null!==Le&&google.maps.event.removeListener(Le),null!==Ee&&google.maps.event.removeListener(Ee),null!==we&&google.maps.event.removeListener(we),null!==ke&&google.maps.event.removeListener(ke),null!==De&&google.maps.event.removeListener(De),null!==Ie&&google.maps.event.removeListener(Ie),null!==Te&&google.maps.event.removeListener(Te),W&&W(t),r?r.removeMarker(t,!!i):t&&t.setMap(null)}}),[]);var Re=n.useMemo((()=>a?n.Children.map(a,(e=>{if(!n.isValidElement(e))return e;var t=e;return n.cloneElement(t,{anchor:H})})):null),[a,H]);return t.jsx(t.Fragment,{children:Re})||null}));class ve extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[])}componentDidMount(){var e=this;return w((function*(){var t=ce(ce(ce({},e.props.options||ge),e.props.clusterer?ge:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(t),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=b({updaterMap:de,eventMap:he,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)}))()}componentDidUpdate(e){this.marker&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:de,eventMap:he,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),y(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){return(this.props.children?n.Children.map(this.props.children,(e=>{if(!n.isValidElement(e))return e;var t=e;return n.cloneElement(t,{anchor:this.marker})})):null)||null}}p(ve,"contextType",d);var fe=function(){function e(t,n){t.getClusterer().extend(e,google.maps.OverlayView),this.cluster=t,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=n,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(t.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return e.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},e.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},e.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var t=this.cluster.getClusterer();if(google.maps.event.trigger(t,"click",this.cluster),google.maps.event.trigger(t,"clusterclick",this.cluster),t.getZoomOnClick()){var n=t.getMaxZoom(),s=this.cluster.getBounds(),o=t.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(s),this.timeOut=window.setTimeout((function(){var e=t.getMap();if(null!==e){"fitBounds"in e&&e.fitBounds(s);var o=e.getZoom()||0;null!==n&&o>n&&e.setZoom(n+1)}}),100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},e.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},e.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},e.prototype.onAdd=function(){var e;this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null===(e=this.getPanes())||void 0===e||e.overlayMouseTarget.appendChild(this.div);var t=this.getMap();null!==t&&(this.boundsChangedListener=google.maps.event.addListener(t,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},e.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=null!==e?"".concat(e.y,"px"):"0",this.div.style.left=null!==e?"".concat(e.x,"px"):"0"}},e.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},e.prototype.show=function(){var e,t,n,s,o,r;if(this.div&&this.center){var i=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,a=this.backgroundPosition.split(" "),l=parseInt((null===(e=a[0])||void 0===e?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),u=parseInt((null===(t=a[1])||void 0===t?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),p=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==p?"".concat(p.y,"px"):"0","; left: ").concat(null!==p?"".concat(p.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var c=document.createElement("img");c.alt=i,c.src=this.url,c.width=this.width,c.height=this.height,c.setAttribute("style","position: absolute; top: ".concat(u,"px; left: ").concat(l,"px")),this.cluster.getClusterer().enableRetinaIcons||(c.style.clip="rect(-".concat(u,"px, -").concat(l+this.width,"px, -").concat(u+this.height,", -").concat(l,")"));var h=document.createElement("div");h.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null===(n=this.sums)||void 0===n?void 0:n.text)&&(h.innerText="".concat(null===(s=this.sums)||void 0===s?void 0:s.text)),(null===(o=this.sums)||void 0===o?void 0:o.html)&&(h.innerHTML="".concat(null===(r=this.sums)||void 0===r?void 0:r.html)),this.div.innerHTML="",this.div.appendChild(c),this.div.appendChild(h),this.div.title=i,this.div.style.display=""}this.visible=!0},e.prototype.useStyle=function(e){this.sums=e;var t=this.cluster.getClusterer().getStyles(),n=t[Math.min(t.length-1,Math.max(0,e.index-1))];n&&(this.url=n.url,this.height=n.height,this.width=n.width,n.className&&(this.className="".concat(this.clusterClassName," ").concat(n.className)),this.anchorText=n.anchorText||[0,0],this.anchorIcon=n.anchorIcon||[this.height/2,this.width/2],this.textColor=n.textColor||"black",this.textSize=n.textSize||11,this.textDecoration=n.textDecoration||"none",this.fontWeight=n.fontWeight||"bold",this.fontStyle=n.fontStyle||"normal",this.fontFamily=n.fontFamily||"Arial,sans-serif",this.backgroundPosition=n.backgroundPosition||"0 0")},e.prototype.setCenter=function(e){this.center=e},e.prototype.getPosFromLatLng=function(e){var t=this.getProjection().fromLatLngToDivPixel(e);return null!==t&&(t.x-=this.anchorIcon[1],t.y-=this.anchorIcon[0]),t},e}(),ye=function(){function e(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new fe(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return e.prototype.getSize=function(){return this.markers.length},e.prototype.getMarkers=function(){return this.markers},e.prototype.getCenter=function(){return this.center},e.prototype.getMap=function(){return this.map},e.prototype.getClusterer=function(){return this.markerClusterer},e.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),t=0,n=this.getMarkers();t<n.length;t++){var s=n[t].getPosition();s&&e.extend(s)}return e},e.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},e.prototype.addMarker=function(e){var t,n;if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter&&(n=e.getPosition())){var s=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(s-1)+n.lat())/s,(this.center.lng()*(s-1)+n.lng())/s),this.calculateBounds()}}else(n=e.getPosition())&&(this.center=n,this.calculateBounds());e.isAdded=!0,this.markers.push(e);var o=this.markers.length,r=this.markerClusterer.getMaxZoom(),i=null===(t=this.map)||void 0===t?void 0:t.getZoom();if(null!==r&&void 0!==i&&i>r)e.getMap()!==this.map&&e.setMap(this.map);else if(o<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(o===this.minClusterSize)for(var a=0,l=this.markers;a<l.length;a++){l[a].setMap(null)}else e.setMap(null);return!0},e.prototype.isMarkerInClusterBounds=function(e){if(null!==this.bounds){var t=e.getPosition();if(t)return this.bounds.contains(t)}return!1},e.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},e.prototype.updateIcon=function(){var e,t=this.markers.length,n=this.markerClusterer.getMaxZoom(),s=null===(e=this.map)||void 0===e?void 0:e.getZoom();null!==n&&void 0!==s&&s>n||t<this.minClusterSize?this.clusterIcon.hide():(this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show())},e.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var t=0;t<this.markers.length;t++)if(e===this.markers[t])return!0;return!1},e}();function be(e,t){var n=e.length,s=n.toString().length,o=Math.min(s,t);return{text:n.toString(),index:o,title:""}}var Le=[53,56,66,78,90],Ce=function(){function e(t,n,s){void 0===n&&(n=[]),void 0===s&&(s={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(e,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=s.gridSize||60,this.minClusterSize=s.minimumClusterSize||2,this.maxZoom=s.maxZoom||null,this.styles=s.styles||[],this.title=s.title||"",this.zoomOnClick=!0,void 0!==s.zoomOnClick&&(this.zoomOnClick=s.zoomOnClick),this.averageCenter=!1,void 0!==s.averageCenter&&(this.averageCenter=s.averageCenter),this.ignoreHidden=!1,void 0!==s.ignoreHidden&&(this.ignoreHidden=s.ignoreHidden),this.enableRetinaIcons=!1,void 0!==s.enableRetinaIcons&&(this.enableRetinaIcons=s.enableRetinaIcons),this.imagePath=s.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=s.imageExtension||"png",this.imageSizes=s.imageSizes||Le,this.calculator=s.calculator||be,this.batchSize=s.batchSize||2e3,this.batchSizeIE=s.batchSizeIE||500,this.clusterClass=s.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(n,!0),this.setMap(t)}return e.prototype.onZoomChanged=function(){var e,t;this.resetViewport(!1),(null===(e=this.getMap())||void 0===e?void 0:e.getZoom())!==(this.get("minZoom")||0)&&(null===(t=this.getMap())||void 0===t?void 0:t.getZoom())!==this.get("maxZoom")||google.maps.event.trigger(this,"idle")},e.prototype.onIdle=function(){this.redraw()},e.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),null!==e&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},e.prototype.onRemove=function(){for(var e=0,t=this.markers;e<t.length;e++){var n=t[e];n.getMap()!==this.activeMap&&n.setMap(this.activeMap)}for(var s=0,o=this.clusters;s<o.length;s++){o[s].remove()}this.clusters=[];for(var r=0,i=this.listeners;r<i.length;r++){var a=i[r];google.maps.event.removeListener(a)}this.listeners=[],this.activeMap=null,this.ready=!1},e.prototype.draw=function(){},e.prototype.getMap=function(){return null},e.prototype.getPanes=function(){return null},e.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},e.prototype.setMap=function(){},e.prototype.addListener=function(){return{remove:function(){}}},e.prototype.bindTo=function(){},e.prototype.get=function(){},e.prototype.notify=function(){},e.prototype.set=function(){},e.prototype.setValues=function(){},e.prototype.unbind=function(){},e.prototype.unbindAll=function(){},e.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},e.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),t=new google.maps.LatLngBounds,n=0,s=e;n<s.length;n++){var o=s[n].getPosition();o&&t.extend(o)}var r=this.getMap();null!==r&&"fitBounds"in r&&r.fitBounds(t)},e.prototype.getGridSize=function(){return this.gridSize},e.prototype.setGridSize=function(e){this.gridSize=e},e.prototype.getMinimumClusterSize=function(){return this.minClusterSize},e.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},e.prototype.getMaxZoom=function(){return this.maxZoom},e.prototype.setMaxZoom=function(e){this.maxZoom=e},e.prototype.getStyles=function(){return this.styles},e.prototype.setStyles=function(e){this.styles=e},e.prototype.getTitle=function(){return this.title},e.prototype.setTitle=function(e){this.title=e},e.prototype.getZoomOnClick=function(){return this.zoomOnClick},e.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},e.prototype.getAverageCenter=function(){return this.averageCenter},e.prototype.setAverageCenter=function(e){this.averageCenter=e},e.prototype.getIgnoreHidden=function(){return this.ignoreHidden},e.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},e.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},e.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},e.prototype.getImageExtension=function(){return this.imageExtension},e.prototype.setImageExtension=function(e){this.imageExtension=e},e.prototype.getImagePath=function(){return this.imagePath},e.prototype.setImagePath=function(e){this.imagePath=e},e.prototype.getImageSizes=function(){return this.imageSizes},e.prototype.setImageSizes=function(e){this.imageSizes=e},e.prototype.getCalculator=function(){return this.calculator},e.prototype.setCalculator=function(e){this.calculator=e},e.prototype.getBatchSizeIE=function(){return this.batchSizeIE},e.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},e.prototype.getClusterClass=function(){return this.clusterClass},e.prototype.setClusterClass=function(e){this.clusterClass=e},e.prototype.getMarkers=function(){return this.markers},e.prototype.getTotalMarkers=function(){return this.markers.length},e.prototype.getClusters=function(){return this.clusters},e.prototype.getTotalClusters=function(){return this.clusters.length},e.prototype.addMarker=function(e,t){this.pushMarkerTo(e),t||this.redraw()},e.prototype.addMarkers=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var s=e[n];s&&this.pushMarkerTo(s)}t||this.redraw()},e.prototype.pushMarkerTo=function(e){var t=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",(function(){t.ready&&(e.isAdded=!1,t.repaint())})),e.isAdded=!1,this.markers.push(e)},e.prototype.removeMarker_=function(e){var t=-1;if(this.markers.indexOf)t=this.markers.indexOf(e);else for(var n=0;n<this.markers.length;n++)if(e===this.markers[n]){t=n;break}return-1!==t&&(e.setMap(null),this.markers.splice(t,1),!0)},e.prototype.removeMarker=function(e,t){var n=this.removeMarker_(e);return!t&&n&&this.repaint(),n},e.prototype.removeMarkers=function(e,t){for(var n=!1,s=0,o=e;s<o.length;s++){var r=o[s];n=n||this.removeMarker_(r)}return!t&&n&&this.repaint(),n},e.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},e.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout((function(){for(var t=0,n=e;t<n.length;t++){n[t].remove()}}),0)},e.prototype.getExtendedBounds=function(e){var t=this.getProjection(),n=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));null!==n&&(n.x+=this.gridSize,n.y-=this.gridSize);var s=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(null!==s&&(s.x-=this.gridSize,s.y+=this.gridSize),null!==n){var o=t.fromDivPixelToLatLng(n);null!==o&&e.extend(o)}if(null!==s){var r=t.fromDivPixelToLatLng(s);null!==r&&e.extend(r)}return e},e.prototype.redraw=function(){this.createClusters(0)},e.prototype.resetViewport=function(e){for(var t=0,n=this.clusters;t<n.length;t++){n[t].remove()}this.clusters=[];for(var s=0,o=this.markers;s<o.length;s++){var r=o[s];r.isAdded=!1,e&&r.setMap(null)}},e.prototype.distanceBetweenPoints=function(e,t){var n=(t.lat()-e.lat())*Math.PI/180,s=(t.lng()-e.lng())*Math.PI/180,o=Math.sin(n/2)*Math.sin(n/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(t.lat()*Math.PI/180)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(o),Math.sqrt(1-o))*6371},e.prototype.isMarkerInBounds=function(e,t){var n=e.getPosition();return!!n&&t.contains(n)},e.prototype.addToClosestCluster=function(e){for(var t,n=4e4,s=null,o=0,r=this.clusters;o<r.length;o++){var i=(t=r[o]).getCenter(),a=e.getPosition();if(i&&a){var l=this.distanceBetweenPoints(i,a);l<n&&(n=l,s=t)}}s&&s.isMarkerInClusterBounds(e)?s.addMarker(e):((t=new ye(this)).addMarker(e),this.clusters.push(t))},e.prototype.createClusters=function(e){var t=this;if(this.ready){0===e&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var n=this.getMap(),s=(null!==n&&"getBounds"in n?n.getBounds():null),o=((null==n?void 0:n.getZoom())||0)>3?new google.maps.LatLngBounds(null==s?void 0:s.getSouthWest(),null==s?void 0:s.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),r=this.getExtendedBounds(o),i=Math.min(e+this.batchSize,this.markers.length),a=e;a<i;a++){var l=this.markers[a];l&&!l.isAdded&&this.isMarkerInBounds(l,r)&&(!this.ignoreHidden||this.ignoreHidden&&l.getVisible())&&this.addToClosestCluster(l)}if(i<this.markers.length)this.timerRefStatic=window.setTimeout((function(){t.createClusters(i)}),0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var u=0,p=this.clusters;u<p.length;u++){p[u].updateIcon()}}}},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype){var n=t;this.prototype[n]=e.prototype[n]}return this}.apply(e,[t])},e}();function Ee(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}var Me={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},we={averageCenter(e,t){e.setAverageCenter(t)},batchSizeIE(e,t){e.setBatchSizeIE(t)},calculator(e,t){e.setCalculator(t)},clusterClass(e,t){e.setClusterClass(t)},enableRetinaIcons(e,t){e.setEnableRetinaIcons(t)},gridSize(e,t){e.setGridSize(t)},ignoreHidden(e,t){e.setIgnoreHidden(t)},imageExtension(e,t){e.setImageExtension(t)},imagePath(e,t){e.setImagePath(t)},imageSizes(e,t){e.setImageSizes(t)},maxZoom(e,t){e.setMaxZoom(t)},minimumClusterSize(e,t){e.setMinimumClusterSize(t)},styles(e,t){e.setStyles(t)},title(e,t){e.setTitle(t)},zoomOnClick(e,t){e.setZoomOnClick(t)}},xe={};var ke=n.memo((function(e){var{children:t,options:s,averageCenter:o,batchSizeIE:r,calculator:i,clusterClass:a,enableRetinaIcons:l,gridSize:u,ignoreHidden:c,imageExtension:h,imagePath:g,imageSizes:m,maxZoom:v,minimumClusterSize:f,styles:y,title:b,zoomOnClick:L,onClick:C,onClusteringBegin:E,onClusteringEnd:M,onMouseOver:w,onMouseOut:x,onLoad:k,onUnmount:P}=e,[S,O]=n.useState(null),D=n.useContext(d),[j,I]=n.useState(null),[B,T]=n.useState(null),[_,R]=n.useState(null),[U,z]=n.useState(null),[A,Z]=n.useState(null);return n.useEffect((()=>{S&&x&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(S,Me.onMouseOut,x)))}),[x]),n.useEffect((()=>{S&&w&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(S,Me.onMouseOver,w)))}),[w]),n.useEffect((()=>{S&&C&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(S,Me.onClick,C)))}),[C]),n.useEffect((()=>{S&&E&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(S,Me.onClusteringBegin,E)))}),[E]),n.useEffect((()=>{S&&M&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(S,Me.onClusteringEnd,M)))}),[M]),n.useEffect((()=>{void 0!==o&&null!==S&&we.averageCenter(S,o)}),[S,o]),n.useEffect((()=>{void 0!==r&&null!==S&&we.batchSizeIE(S,r)}),[S,r]),n.useEffect((()=>{void 0!==i&&null!==S&&we.calculator(S,i)}),[S,i]),n.useEffect((()=>{void 0!==a&&null!==S&&we.clusterClass(S,a)}),[S,a]),n.useEffect((()=>{void 0!==l&&null!==S&&we.enableRetinaIcons(S,l)}),[S,l]),n.useEffect((()=>{void 0!==u&&null!==S&&we.gridSize(S,u)}),[S,u]),n.useEffect((()=>{void 0!==c&&null!==S&&we.ignoreHidden(S,c)}),[S,c]),n.useEffect((()=>{void 0!==h&&null!==S&&we.imageExtension(S,h)}),[S,h]),n.useEffect((()=>{void 0!==g&&null!==S&&we.imagePath(S,g)}),[S,g]),n.useEffect((()=>{void 0!==m&&null!==S&&we.imageSizes(S,m)}),[S,m]),n.useEffect((()=>{void 0!==v&&null!==S&&we.maxZoom(S,v)}),[S,v]),n.useEffect((()=>{void 0!==f&&null!==S&&we.minimumClusterSize(S,f)}),[S,f]),n.useEffect((()=>{void 0!==y&&null!==S&&we.styles(S,y)}),[S,y]),n.useEffect((()=>{void 0!==b&&null!==S&&we.title(S,b)}),[S,b]),n.useEffect((()=>{void 0!==L&&null!==S&&we.zoomOnClick(S,L)}),[S,L]),n.useEffect((()=>{if(D){var e=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ee(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ee(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},s||xe),t=new Ce(D,[],e);return o&&we.averageCenter(t,o),r&&we.batchSizeIE(t,r),i&&we.calculator(t,i),a&&we.clusterClass(t,a),l&&we.enableRetinaIcons(t,l),u&&we.gridSize(t,u),c&&we.ignoreHidden(t,c),h&&we.imageExtension(t,h),g&&we.imagePath(t,g),m&&we.imageSizes(t,m),v&&we.maxZoom(t,v),f&&we.minimumClusterSize(t,f),y&&we.styles(t,y),b&&we.title(t,b),L&&we.zoomOnClick(t,L),x&&z(google.maps.event.addListener(t,Me.onMouseOut,x)),w&&Z(google.maps.event.addListener(t,Me.onMouseOver,w)),C&&I(google.maps.event.addListener(t,Me.onClick,C)),E&&T(google.maps.event.addListener(t,Me.onClusteringBegin,E)),M&&R(google.maps.event.addListener(t,Me.onClusteringEnd,M)),O(t),k&&k(t),()=>{null!==U&&google.maps.event.removeListener(U),null!==A&&google.maps.event.removeListener(A),null!==j&&google.maps.event.removeListener(j),null!==B&&google.maps.event.removeListener(B),null!==_&&google.maps.event.removeListener(_),P&&P(t)}}}),[]),null!==S&&t(S)||null}));class Pe extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{markerClusterer:null}),p(this,"setClustererCallback",(()=>{null!==this.state.markerClusterer&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)}))}componentDidMount(){if(this.context){var e=new Ce(this.context,[],this.props.options);this.registeredEvents=b({updaterMap:we,eventMap:Me,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({markerClusterer:e})),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:we,eventMap:Me,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){null!==this.state.markerClusterer&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),y(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return null!==this.state.markerClusterer?this.props.children(this.state.markerClusterer):null}}function Se(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}p(Pe,"contextType",d);var Oe=function(){function e(t){void 0===t&&(t={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(e,google.maps.OverlayView),this.content=t.content||"",this.disableAutoPan=t.disableAutoPan||!1,this.maxWidth=t.maxWidth||0,this.pixelOffset=t.pixelOffset||new google.maps.Size(0,0),this.position=t.position||new google.maps.LatLng(0,0),this.zIndex=t.zIndex||null,this.boxClass=t.boxClass||"infoBox",this.boxStyle=t.boxStyle||{},this.closeBoxMargin=t.closeBoxMargin||"2px",this.closeBoxURL=t.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===t.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=t.infoBoxClearance||new google.maps.Size(1,1),void 0===t.visible&&(void 0===t.isHidden?t.visible=!0:t.visible=!t.isHidden),this.isHidden=!t.visible,this.alignBottom=t.alignBottom||!1,this.pane=t.pane||"floatPane",this.enableEventPropagation=t.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return e.prototype.createInfoBoxDiv=function(){var e=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var t=this.getPanes();if(null!==t&&t[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var n=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-n.left-n.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var s=0,o=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];s<o.length;s++){var r=o[s];this.eventListeners.push(google.maps.event.addListener(this.div,r,Se))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",(function(){e.div&&(e.div.style.cursor="default")})))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",(function(t){t.returnValue=!1,t.preventDefault&&t.preventDefault(),e.enableEventPropagation||Se(t)})),google.maps.event.trigger(this,"domready")}},e.prototype.getCloseBoxImg=function(){var e="";return""!==this.closeBoxURL&&(e='<img alt=""',e+=' aria-hidden="true"',e+=" src='"+this.closeBoxURL+"'",e+=" align=right",e+=" style='",e+=" position: relative;",e+=" cursor: pointer;",e+=" margin: "+this.closeBoxMargin+";",e+="'>"),e},e.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},e.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},e.prototype.getCloseClickHandler=function(){return this.closeClickHandler},e.prototype.panBox=function(e){if(this.div&&!e){var t=this.getMap();if(t instanceof google.maps.Map){var n=0,s=0,o=t.getBounds();o&&!o.contains(this.position)&&t.setCenter(this.position);var r=t.getDiv(),i=r.offsetWidth,a=r.offsetHeight,l=this.pixelOffset.width,u=this.pixelOffset.height,p=this.div.offsetWidth,c=this.div.offsetHeight,h=this.infoBoxClearance.width,d=this.infoBoxClearance.height,g=this.getProjection().fromLatLngToContainerPixel(this.position);null!==g&&(g.x<-l+h?n=g.x+l-h:g.x+p+l+h>i&&(n=g.x+p+l+h-i),this.alignBottom?g.y<-u+d+c?s=g.y+u-d-c:g.y+u+d>a&&(s=g.y+u+d-a):g.y<-u+d?s=g.y+u-d:g.y+c+u+d>a&&(s=g.y+c+u+d-a)),0===n&&0===s||t.panBy(n,s)}}},e.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.div.style[t]=e[t]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var n=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*n+')"',this.div.style.filter="alpha(opacity="+100*n+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},e.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var t=this.div.ownerDocument,n=t&&t.defaultView?t.defaultView.getComputedStyle(this.div,""):null;n&&(e.top=parseInt(n.borderTopWidth||"",10)||0,e.bottom=parseInt(n.borderBottomWidth||"",10)||0,e.left=parseInt(n.borderLeftWidth||"",10)||0,e.right=parseInt(n.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var s=this.div.currentStyle;s&&(e.top=parseInt(s.borderTopWidth||"",10)||0,e.bottom=parseInt(s.borderBottomWidth||"",10)||0,e.left=parseInt(s.borderLeftWidth||"",10)||0,e.right=parseInt(s.borderRightWidth||"",10)||0)}return e},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},e.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection().fromLatLngToDivPixel(this.position);null!==e&&(this.div.style.left=e.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(e.y+this.pixelOffset.height)+"px":this.div.style.top=e.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},e.prototype.setOptions=function(e){void 0===e&&(e={}),void 0!==e.boxClass&&(this.boxClass=e.boxClass,this.setBoxStyle()),void 0!==e.boxStyle&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),void 0!==e.content&&this.setContent(e.content),void 0!==e.disableAutoPan&&(this.disableAutoPan=e.disableAutoPan),void 0!==e.maxWidth&&(this.maxWidth=e.maxWidth),void 0!==e.pixelOffset&&(this.pixelOffset=e.pixelOffset),void 0!==e.alignBottom&&(this.alignBottom=e.alignBottom),void 0!==e.position&&this.setPosition(e.position),void 0!==e.zIndex&&this.setZIndex(e.zIndex),void 0!==e.closeBoxMargin&&(this.closeBoxMargin=e.closeBoxMargin),void 0!==e.closeBoxURL&&(this.closeBoxURL=e.closeBoxURL),void 0!==e.infoBoxClearance&&(this.infoBoxClearance=e.infoBoxClearance),void 0!==e.isHidden&&(this.isHidden=e.isHidden),void 0!==e.visible&&(this.isHidden=!e.visible),void 0!==e.enableEventPropagation&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},e.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},e.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},e.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},e.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},e.prototype.getContent=function(){return this.content},e.prototype.getPosition=function(){return this.position},e.prototype.getZIndex=function(){return this.zIndex},e.prototype.getVisible=function(){var e=this.getMap();return null!=e&&!this.isHidden},e.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},e.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},e.prototype.open=function(e,t){var n=this;t&&(this.position=t.getPosition(),this.moveListener=google.maps.event.addListener(t,"position_changed",(function(){var e=t.getPosition();n.setPosition(e)})),this.mapListener=google.maps.event.addListener(t,"map_changed",(function(){n.setMap(t.map)}))),this.setMap(e),this.div&&this.panBox()},e.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,t=this.eventListeners;e<t.length;e++){var n=t[e];google.maps.event.removeListener(n)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype)Object.prototype.hasOwnProperty.call(this,t)||(this.prototype[t]=e.prototype[t]);return this}.apply(e,[t])},e}(),De=["position"],je=["position"];function Ie(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Be(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ie(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ie(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Te={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},_e={options(e,t){e.setOptions(t)},position(e,t){t instanceof google.maps.LatLng?e.setPosition(t):e.setPosition(new google.maps.LatLng(t.lat,t.lng))},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},Re={};var Ue,ze,Ae=n.memo((function(e){var{children:t,anchor:o,options:r,position:i,zIndex:a,onCloseClick:l,onDomReady:u,onContentChanged:p,onPositionChanged:c,onZindexChanged:g,onLoad:m,onUnmount:v}=e,f=n.useContext(d),[y,b]=n.useState(null),[L,C]=n.useState(null),[E,M]=n.useState(null),[w,x]=n.useState(null),[k,P]=n.useState(null),[S,O]=n.useState(null),D=n.useRef(null);return n.useEffect((()=>{f&&null!==y&&(y.close(),o?y.open(f,o):y.getPosition()&&y.open(f))}),[f,y,o]),n.useEffect((()=>{r&&null!==y&&y.setOptions(r)}),[y,r]),n.useEffect((()=>{if(i&&null!==y){var e=i instanceof google.maps.LatLng?i:new google.maps.LatLng(i.lat,i.lng);y.setPosition(e)}}),[i]),n.useEffect((()=>{"number"==typeof a&&null!==y&&y.setZIndex(a)}),[a]),n.useEffect((()=>{y&&l&&(null!==L&&google.maps.event.removeListener(L),C(google.maps.event.addListener(y,"closeclick",l)))}),[l]),n.useEffect((()=>{y&&u&&(null!==E&&google.maps.event.removeListener(E),M(google.maps.event.addListener(y,"domready",u)))}),[u]),n.useEffect((()=>{y&&p&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"content_changed",p)))}),[p]),n.useEffect((()=>{y&&c&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",c)))}),[c]),n.useEffect((()=>{y&&g&&(null!==S&&google.maps.event.removeListener(S),O(google.maps.event.addListener(y,"zindex_changed",g)))}),[g]),n.useEffect((()=>{if(f){var e,t=r||Re,{position:n}=t,s=_(t,De);!n||n instanceof google.maps.LatLng||(e=new google.maps.LatLng(n.lat,n.lng));var i=new Oe(Be(Be({},s),e?{position:e}:{}));D.current=document.createElement("div"),b(i),l&&C(google.maps.event.addListener(i,"closeclick",l)),u&&M(google.maps.event.addListener(i,"domready",u)),p&&x(google.maps.event.addListener(i,"content_changed",p)),c&&P(google.maps.event.addListener(i,"position_changed",c)),g&&O(google.maps.event.addListener(i,"zindex_changed",g)),i.setContent(D.current),o?i.open(f,o):i.getPosition()?i.open(f):h(!1,"You must provide either an anchor or a position prop for <InfoBox>."),m&&m(i)}return()=>{null!==y&&(L&&google.maps.event.removeListener(L),w&&google.maps.event.removeListener(w),E&&google.maps.event.removeListener(E),k&&google.maps.event.removeListener(k),S&&google.maps.event.removeListener(S),v&&v(y),y.close())}}),[]),D.current?s.createPortal(n.Children.only(t),D.current):null}));class Ze extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"containerElement",null),p(this,"state",{infoBox:null}),p(this,"open",((e,t)=>{t?null!==this.context&&e.open(this.context,t):e.getPosition()?null!==this.context&&e.open(this.context):h(!1,"You must provide either an anchor or a position prop for <InfoBox>.")})),p(this,"setInfoBoxCallback",(()=>{null!==this.state.infoBox&&null!==this.containerElement&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))}))}componentDidMount(){var e,t=this.props.options||{},{position:n}=t,s=_(t,je);!n||n instanceof google.maps.LatLng||(e=new google.maps.LatLng(n.lat,n.lng));var o=new Oe(Be(Be({},s),e?{position:e}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=b({updaterMap:_e,eventMap:Te,prevProps:{},nextProps:this.props,instance:o}),this.setState({infoBox:o},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:t}=this.state;null!==t&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:_e,eventMap:Te,prevProps:e,nextProps:this.props,instance:t}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:t}=this.state;null!==t&&(e&&e(t),y(this.registeredEvents),t.close())}render(){return this.containerElement?s.createPortal(n.Children.only(this.props.children),this.containerElement):null}}p(Ze,"contextType",d);var Ve=(ze||(ze=1,Ue=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var s,o,r;if(Array.isArray(t)){if((s=t.length)!=n.length)return!1;for(o=s;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((s=(r=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=s;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,r[o]))return!1;for(o=s;0!=o--;){var i=r[o];if(!e(t[i],n[i]))return!1}return!0}return t!=t&&n!=n}),Ue),We=c(Ve),Ne=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];class He{static from(e){if(!(e instanceof ArrayBuffer))throw new Error("Data must be an instance of ArrayBuffer.");var[t,n]=new Uint8Array(e,0,2);if(219!==t)throw new Error("Data does not appear to be in a KDBush format.");var s=n>>4;if(1!==s)throw new Error("Got v".concat(s," data when expected v").concat(1,"."));var o=Ne[15&n];if(!o)throw new Error("Unrecognized array type.");var[r]=new Uint16Array(e,2,1),[i]=new Uint32Array(e,4,1);return new He(i,r,o,e)}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Float64Array,s=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw new Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=n,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var o=Ne.indexOf(this.ArrayType),r=2*e*this.ArrayType.BYTES_PER_ELEMENT,i=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-i%8)%8;if(o<0)throw new Error("Unexpected typed array class: ".concat(n,"."));s&&s instanceof ArrayBuffer?(this.data=s,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+i+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+r+i+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+i+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+o]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){var n=this._pos>>1;return this.ids[n]=n,this.coords[this._pos++]=e,this.coords[this._pos++]=t,n}finish(){var e=this._pos>>1;if(e!==this.numItems)throw new Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return Fe(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,n,s){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:r,nodeSize:i}=this,a=[0,o.length-1,0],l=[];a.length;){var u=a.pop()||0,p=a.pop()||0,c=a.pop()||0;if(p-c<=i)for(var h=c;h<=p;h++){var d=r[2*h],g=r[2*h+1];d>=e&&d<=n&&g>=t&&g<=s&&l.push(o[h])}else{var m=c+p>>1,v=r[2*m],f=r[2*m+1];v>=e&&v<=n&&f>=t&&f<=s&&l.push(o[m]),(0===u?e<=v:t<=f)&&(a.push(c),a.push(m-1),a.push(1-u)),(0===u?n>=v:s>=f)&&(a.push(m+1),a.push(p),a.push(1-u))}}return l}within(e,t,n){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:s,coords:o,nodeSize:r}=this,i=[0,s.length-1,0],a=[],l=n*n;i.length;){var u=i.pop()||0,p=i.pop()||0,c=i.pop()||0;if(p-c<=r)for(var h=c;h<=p;h++)qe(o[2*h],o[2*h+1],e,t)<=l&&a.push(s[h]);else{var d=c+p>>1,g=o[2*d],m=o[2*d+1];qe(g,m,e,t)<=l&&a.push(s[d]),(0===u?e-n<=g:t-n<=m)&&(i.push(c),i.push(d-1),i.push(1-u)),(0===u?e+n>=g:t+n>=m)&&(i.push(d+1),i.push(p),i.push(1-u))}}return a}}function Fe(e,t,n,s,o,r){if(!(o-s<=n)){var i=s+o>>1;Ge(e,t,i,s,o,r),Fe(e,t,n,s,i-1,1-r),Fe(e,t,n,i+1,o,1-r)}}function Ge(e,t,n,s,o,r){for(;o>s;){if(o-s>600){var i=o-s+1,a=n-s+1,l=Math.log(i),u=.5*Math.exp(2*l/3),p=.5*Math.sqrt(l*u*(i-u)/i)*(a-i/2<0?-1:1);Ge(e,t,n,Math.max(s,Math.floor(n-a*u/i+p)),Math.min(o,Math.floor(n+(i-a)*u/i+p)),r)}var c=t[2*n+r],h=s,d=o;for(Ke(e,t,s,n),t[2*o+r]>c&&Ke(e,t,s,o);h<d;){for(Ke(e,t,h,d),h++,d--;t[2*h+r]<c;)h++;for(;t[2*d+r]>c;)d--}t[2*s+r]===c?Ke(e,t,s,d):Ke(e,t,++d,o),d<=n&&(s=d+1),n<=d&&(o=d-1)}}function Ke(e,t,n,s){Ye(e,n,s),Ye(t,2*n,2*s),Ye(t,2*n+1,2*s+1)}function Ye(e,t,n){var s=e[t];e[t]=e[n],e[n]=s}function qe(e,t,n,s){var o=e-n,r=t-s;return o*o+r*r}var Je,Xe={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},$e=Math.fround||(Je=new Float32Array(1),e=>(Je[0]=+e,Je[0]));class Qe{constructor(e){this.options=Object.assign(Object.create(Xe),e),this.trees=new Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:t,minZoom:n,maxZoom:s}=this.options;t&&console.time("total time");var o="prepare ".concat(e.length," points");t&&console.time(o),this.points=e;for(var r=[],i=0;i<e.length;i++){var a=e[i];if(a.geometry){var[l,u]=a.geometry.coordinates,p=$e(nt(l)),c=$e(st(u));r.push(p,c,1/0,i,-1,1),this.options.reduce&&r.push(0)}}var h=this.trees[s+1]=this._createTree(r);t&&console.timeEnd(o);for(var d=s;d>=n;d--){var g=+Date.now();h=this.trees[d]=this._createTree(this._cluster(h,d)),t&&console.log("z%d: %d clusters in %dms",d,h.numItems,+Date.now()-g)}return t&&console.timeEnd("total time"),this}getClusters(e,t){var n=((e[0]+180)%360+360)%360-180,s=Math.max(-90,Math.min(90,e[1])),o=180===e[2]?180:((e[2]+180)%360+360)%360-180,r=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)n=-180,o=180;else if(n>o){var i=this.getClusters([n,s,180,r],t),a=this.getClusters([-180,s,o,r],t);return i.concat(a)}var l=this.trees[this._limitZoom(t)],u=l.range(nt(n),st(r),nt(o),st(s)),p=l.data,c=[];for(var h of u){var d=this.stride*h;c.push(p[d+5]>1?et(p,d,this.clusterProps):this.points[p[d+3]])}return c}getChildren(e){var t=this._getOriginId(e),n=this._getOriginZoom(e),s="No cluster with the specified id.",o=this.trees[n];if(!o)throw new Error(s);var r=o.data;if(t*this.stride>=r.length)throw new Error(s);var i=this.options.radius/(this.options.extent*Math.pow(2,n-1)),a=r[t*this.stride],l=r[t*this.stride+1],u=o.within(a,l,i),p=[];for(var c of u){var h=c*this.stride;r[h+4]===e&&p.push(r[h+5]>1?et(r,h,this.clusterProps):this.points[r[h+3]])}if(0===p.length)throw new Error(s);return p}getLeaves(e,t,n){t=t||10,n=n||0;var s=[];return this._appendLeaves(s,e,t,n,0),s}getTile(e,t,n){var s=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:r,radius:i}=this.options,a=i/r,l=(n-a)/o,u=(n+1+a)/o,p={features:[]};return this._addTileFeatures(s.range((t-a)/o,l,(t+1+a)/o,u),s.data,t,n,o,p),0===t&&this._addTileFeatures(s.range(1-a/o,l,1,u),s.data,o,n,o,p),t===o-1&&this._addTileFeatures(s.range(0,l,a/o,u),s.data,-1,n,o,p),p.features.length?p:null}getClusterExpansionZoom(e){for(var t=this._getOriginZoom(e)-1;t<=this.options.maxZoom;){var n=this.getChildren(e);if(t++,1!==n.length)break;e=n[0].properties.cluster_id}return t}_appendLeaves(e,t,n,s,o){var r=this.getChildren(t);for(var i of r){var a=i.properties;if(a&&a.cluster?o+a.point_count<=s?o+=a.point_count:o=this._appendLeaves(e,a.cluster_id,n,s,o):o<s?o++:e.push(i),e.length===n)break}return o}_createTree(e){for(var t=new He(e.length/this.stride|0,this.options.nodeSize,Float32Array),n=0;n<e.length;n+=this.stride)t.add(e[n],e[n+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,n,s,o,r){for(var i of e){var a=i*this.stride,l=t[a+5]>1,u=void 0,p=void 0,c=void 0;if(l)u=tt(t,a,this.clusterProps),p=t[a],c=t[a+1];else{var h=this.points[t[a+3]];u=h.properties;var[d,g]=h.geometry.coordinates;p=nt(d),c=st(g)}var m={type:1,geometry:[[Math.round(this.options.extent*(p*o-n)),Math.round(this.options.extent*(c*o-s))]],tags:u},v=void 0;void 0!==(v=l||this.options.generateId?t[a+3]:this.points[t[a+3]].id)&&(m.id=v),r.features.push(m)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){for(var{radius:n,extent:s,reduce:o,minPoints:r}=this.options,i=n/(s*Math.pow(2,t)),a=e.data,l=[],u=this.stride,p=0;p<a.length;p+=u)if(!(a[p+2]<=t)){a[p+2]=t;var c=a[p],h=a[p+1],d=e.within(a[p],a[p+1],i),g=a[p+5],m=g;for(var v of d){var f=v*u;a[f+2]>t&&(m+=a[f+5])}if(m>g&&m>=r){var y=c*g,b=h*g,L=void 0,C=-1,E=(p/u<<5)+(t+1)+this.points.length;for(var M of d){var w=M*u;if(!(a[w+2]<=t)){a[w+2]=t;var x=a[w+5];y+=a[w]*x,b+=a[w+1]*x,a[w+4]=E,o&&(L||(L=this._map(a,p,!0),C=this.clusterProps.length,this.clusterProps.push(L)),o(L,this._map(a,w)))}}a[p+4]=E,l.push(y/m,b/m,1/0,E,-1,m),o&&l.push(C)}else{for(var k=0;k<u;k++)l.push(a[p+k]);if(m>1)for(var P of d){var S=P*u;if(!(a[S+2]<=t)){a[S+2]=t;for(var O=0;O<u;O++)l.push(a[S+O])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,n){if(e[t+5]>1){var s=this.clusterProps[e[t+6]];return n?Object.assign({},s):s}var o=this.points[e[t+3]].properties,r=this.options.map(o);return n&&r===o?Object.assign({},r):r}}function et(e,t,n){return{type:"Feature",id:e[t+3],properties:tt(e,t,n),geometry:{type:"Point",coordinates:[(r=e[t],360*(r-.5)),(s=e[t+1],o=(180-360*s)*Math.PI/180,360*Math.atan(Math.exp(o))/Math.PI-90)]}};var s,o,r;
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */}function tt(e,t,n){var s=e[t+5],o=s>=1e4?"".concat(Math.round(s/1e3),"k"):s>=1e3?"".concat(Math.round(s/100)/10,"k"):s,r=e[t+6],i=-1===r?{}:Object.assign({},n[r]);return Object.assign(i,{cluster:!0,cluster_id:e[t+3],point_count:s,point_count_abbreviated:o})}function nt(e){return e/360+.5}function st(e){var t=Math.sin(e*Math.PI/180),n=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return n<0?0:n>1?1:n}function ot(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(s=Object.getOwnPropertySymbols(e);o<s.length;o++)t.indexOf(s[o])<0&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(n[s[o]]=e[s[o]])}return n}class rt{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}}class it{constructor(e){var{markers:t,position:n}=e;this.markers=t,n&&(n instanceof google.maps.LatLng?this._position=n:this._position=new google.maps.LatLng(n))}get bounds(){if(0!==this.markers.length||this._position){var e=new google.maps.LatLngBounds(this._position,this._position);for(var t of this.markers)e.extend(rt.getPosition(t));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter((e=>rt.getVisible(e))).length}push(e){this.markers.push(e)}delete(){this.marker&&(rt.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}var at=(e,t,n,s)=>{var o=lt(e.getBounds(),t,s);return n.filter((e=>o.contains(rt.getPosition(e))))},lt=(e,t,n)=>{var{northEast:s,southWest:o}=ct(e,t),r=ht({northEast:s,southWest:o},n);return dt(r,t)},ut=(e,t,n)=>{var s=lt(e,t,n),o=s.getNorthEast(),r=s.getSouthWest();return[r.lng(),r.lat(),o.lng(),o.lat()]},pt=(e,t)=>{var n=(t.lat-e.lat)*Math.PI/180,s=(t.lng-e.lng)*Math.PI/180,o=Math.sin(n/2),r=Math.sin(s/2),i=o*o+Math.cos(e.lat*Math.PI/180)*Math.cos(t.lat*Math.PI/180)*r*r;return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))},ct=(e,t)=>({northEast:t.fromLatLngToDivPixel(e.getNorthEast()),southWest:t.fromLatLngToDivPixel(e.getSouthWest())}),ht=(e,t)=>{var{northEast:n,southWest:s}=e;return n.x+=t,n.y-=t,s.x-=t,s.y+=t,{northEast:n,southWest:s}},dt=(e,t)=>{var{northEast:n,southWest:s}=e,o=t.fromDivPixelToLatLng(s),r=t.fromDivPixelToLatLng(n);return new google.maps.LatLngBounds(o,r)};class gt{constructor(e){var{maxZoom:t=16}=e;this.maxZoom=t}noop(e){var{markers:t}=e;return ft(t)}}class mt extends gt{constructor(e){var{viewportPadding:t=60}=e;super(ot(e,["viewportPadding"])),this.viewportPadding=60,this.viewportPadding=t}calculate(e){var{markers:t,map:n,mapCanvasProjection:s}=e;return n.getZoom()>=this.maxZoom?{clusters:this.noop({markers:t}),changed:!1}:{clusters:this.cluster({markers:at(n,s,t,this.viewportPadding),map:n,mapCanvasProjection:s})}}}var vt,ft=e=>e.map((e=>new it({position:rt.getPosition(e),markers:[e]})));class yt extends gt{constructor(e){var{maxZoom:t,radius:n=60}=e,s=ot(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new Qe(Object.assign({maxZoom:this.maxZoom,radius:n},s))}calculate(e){var t=!1,n={zoom:e.map.getZoom()};if(!We(e.markers,this.markers)){t=!0,this.markers=[...e.markers];var s=this.markers.map((e=>{var t=rt.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(s)}return t||(this.state.zoom<=this.maxZoom||n.zoom<=this.maxZoom)&&(t=!We(this.state,n)),this.state=n,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster(e){var{map:t}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map((e=>this.transformCluster(e)))}transformCluster(e){var{geometry:{coordinates:[t,n]},properties:s}=e;if(s.cluster)return new it({markers:this.superCluster.getLeaves(s.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:n,lng:t}});var o=s.marker;return new it({markers:[o],position:rt.getPosition(o)})}}class bt{constructor(e,t){this.markers={sum:e.length};var n=t.map((e=>e.count)),s=n.reduce(((e,t)=>e+t),0);this.clusters={count:t.length,markers:{mean:s/t.length,sum:s,min:Math.min(...n),max:Math.max(...n)}}}}class Lt{render(e,t,n){var{count:s,position:o}=e,r=s>Math.max(10,t.clusters.markers.mean)?"#ff0000":"#0000ff",i='<svg fill="'.concat(r,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">').concat(s,"</text>\n</svg>"),a="Cluster of ".concat(s," markers"),l=Number(google.maps.Marker.MAX_ZINDEX)+s;if(rt.isAdvancedMarkerAvailable(n)){var u=(new DOMParser).parseFromString(i,"image/svg+xml").documentElement;u.setAttribute("transform","translate(0 25)");var p={map:n,position:o,zIndex:l,title:a,content:u};return new google.maps.marker.AdvancedMarkerElement(p)}var c={position:o,zIndex:l,title:a,icon:{url:"data:image/svg+xml;base64,".concat(btoa(i)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(c)}}class Ct{constructor(){!function(e,t){for(var n in t.prototype)e.prototype[n]=t.prototype[n]}(Ct,google.maps.OverlayView)}}!function(e){e.CLUSTERING_BEGIN="clusteringbegin",e.CLUSTERING_END="clusteringend",e.CLUSTER_CLICK="click"}(vt||(vt={}));var Et=(e,t,n)=>{n.fitBounds(t.bounds)};class Mt extends Ct{constructor(e){var{map:t,markers:n=[],algorithmOptions:s={},algorithm:o=new yt(s),renderer:r=new Lt,onClusterClick:i=Et}=e;super(),this.markers=[...n],this.clusters=[],this.algorithm=o,this.renderer=r,this.onClusterClick=i,t&&this.setMap(t)}addMarker(e,t){this.markers.includes(e)||(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach((e=>{this.addMarker(e,!0)})),t||this.render()}removeMarker(e,t){var n=this.markers.indexOf(e);return-1!==n&&(rt.setMap(e,null),this.markers.splice(n,1),t||this.render(),!0)}removeMarkers(e,t){var n=!1;return e.forEach((e=>{n=this.removeMarker(e,!0)||n})),n&&!t&&this.render(),n}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,vt.CLUSTERING_BEGIN,this);var{clusters:t,changed:n}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(n||null==n){var s=new Set;for(var o of t)1==o.markers.length&&s.add(o.markers[0]);var r=[];for(var i of this.clusters)null!=i.marker&&(1==i.markers.length?s.has(i.marker)||rt.setMap(i.marker,null):r.push(i.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame((()=>r.forEach((e=>rt.setMap(e,null)))))}google.maps.event.trigger(this,vt.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach((e=>rt.setMap(e,null))),this.clusters.forEach((e=>e.delete())),this.clusters=[]}renderClusters(){var e=new bt(this.markers,this.clusters),t=this.getMap();this.clusters.forEach((n=>{1===n.markers.length?n.marker=n.markers[0]:(n.marker=this.renderer.render(n,e,t),n.markers.forEach((e=>rt.setMap(e,null))),this.onClusterClick&&n.marker.addListener("click",(e=>{google.maps.event.trigger(this,vt.CLUSTER_CLICK,n),this.onClusterClick(e,n,t)}))),rt.setMap(n.marker,t)}))}}var wt=Object.freeze({__proto__:null,AbstractAlgorithm:gt,AbstractViewportAlgorithm:mt,Cluster:it,ClusterStats:bt,DefaultRenderer:Lt,GridAlgorithm:class extends mt{constructor(e){var{maxDistance:t=4e4,gridSize:n=40}=e;super(ot(e,["maxDistance","gridSize"])),this.clusters=[],this.state={zoom:-1},this.maxDistance=t,this.gridSize=n}calculate(e){var{markers:t,map:n,mapCanvasProjection:s}=e,o={zoom:n.getZoom()},r=!1;return this.state.zoom>=this.maxZoom&&o.zoom>=this.maxZoom||(r=!We(this.state,o)),this.state=o,n.getZoom()>=this.maxZoom?{clusters:this.noop({markers:t}),changed:r}:{clusters:this.cluster({markers:at(n,s,t,this.viewportPadding),map:n,mapCanvasProjection:s})}}cluster(e){var{markers:t,map:n,mapCanvasProjection:s}=e;return this.clusters=[],t.forEach((e=>{this.addToClosestCluster(e,n,s)})),this.clusters}addToClosestCluster(e,t,n){for(var s=this.maxDistance,o=null,r=0;r<this.clusters.length;r++){var i=this.clusters[r],a=pt(i.bounds.getCenter().toJSON(),rt.getPosition(e).toJSON());a<s&&(s=a,o=i)}if(o&&lt(o.bounds,n,this.gridSize).contains(rt.getPosition(e)))o.push(e);else{var l=new it({markers:[e]});this.clusters.push(l)}}},MarkerClusterer:Mt,get MarkerClustererEvents(){return vt},MarkerUtils:rt,NoopAlgorithm:class extends gt{constructor(e){super(ot(e,[]))}calculate(e){var{markers:t,map:n,mapCanvasProjection:s}=e;return{clusters:this.cluster({markers:t,map:n,mapCanvasProjection:s}),changed:!1}}cluster(e){return this.noop(e)}},SuperClusterAlgorithm:yt,SuperClusterViewportAlgorithm:class extends mt{constructor(e){var{maxZoom:t,radius:n=60,viewportPadding:s=60}=e,o=ot(e,["maxZoom","radius","viewportPadding"]);super({maxZoom:t,viewportPadding:s}),this.superCluster=new Qe(Object.assign({maxZoom:this.maxZoom,radius:n},o)),this.state={zoom:-1,view:[0,0,0,0]}}calculate(e){var t={zoom:Math.round(e.map.getZoom()),view:ut(e.map.getBounds(),e.mapCanvasProjection,this.viewportPadding)},n=!We(this.state,t);if(!We(e.markers,this.markers)){n=!0,this.markers=[...e.markers];var s=this.markers.map((e=>{var t=rt.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(s)}return n&&(this.clusters=this.cluster(e),this.state=t),{clusters:this.clusters,changed:n}}cluster(e){var{map:t,mapCanvasProjection:n}=e,s={zoom:Math.round(t.getZoom()),view:ut(t.getBounds(),n,this.viewportPadding)};return this.superCluster.getClusters(s.view,s.zoom).map((e=>this.transformCluster(e)))}transformCluster(e){var{geometry:{coordinates:[t,n]},properties:s}=e;if(s.cluster)return new it({markers:this.superCluster.getLeaves(s.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:n,lng:t}});var o=s.marker;return new it({markers:[o],position:rt.getPosition(o)})}},defaultOnClusterClickHandler:Et,distanceBetweenPoints:pt,extendBoundsToPaddedViewport:lt,extendPixelBounds:ht,filterMarkersToPaddedViewport:at,getPaddedViewport:ut,noop:ft,pixelBoundsToLatLngBounds:dt});function xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function kt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Pt=n.memo((function(e){var{children:t,options:s}=e,o=function(e){var t=g(),[s,o]=n.useState(null);return n.useEffect((()=>{if(t&&null===s){var n=new Mt(kt(kt({},e),{},{map:t}));o(n)}}),[t]),s}(s);return null!==o?t(o):null})),St={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Ot={options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},zIndex(e,t){e.setZIndex(t)}};var Dt=n.memo((function(e){var{children:t,anchor:o,options:r,position:i,zIndex:a,onCloseClick:l,onDomReady:u,onContentChanged:p,onPositionChanged:c,onZindexChanged:g,onLoad:m,onUnmount:v}=e,f=n.useContext(d),[y,b]=n.useState(null),[L,C]=n.useState(null),[E,M]=n.useState(null),[w,x]=n.useState(null),[k,P]=n.useState(null),[S,O]=n.useState(null),D=n.useRef(null);return n.useEffect((()=>{null!==y&&(y.close(),o?y.open(f,o):y.getPosition()&&y.open(f))}),[f,y,o]),n.useEffect((()=>{r&&null!==y&&y.setOptions(r)}),[y,r]),n.useEffect((()=>{i&&null!==y&&y.setPosition(i)}),[i]),n.useEffect((()=>{"number"==typeof a&&null!==y&&y.setZIndex(a)}),[a]),n.useEffect((()=>{y&&l&&(null!==L&&google.maps.event.removeListener(L),C(google.maps.event.addListener(y,"closeclick",l)))}),[l]),n.useEffect((()=>{y&&u&&(null!==E&&google.maps.event.removeListener(E),M(google.maps.event.addListener(y,"domready",u)))}),[u]),n.useEffect((()=>{y&&p&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"content_changed",p)))}),[p]),n.useEffect((()=>{y&&c&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",c)))}),[c]),n.useEffect((()=>{y&&g&&(null!==S&&google.maps.event.removeListener(S),O(google.maps.event.addListener(y,"zindex_changed",g)))}),[g]),n.useEffect((()=>{var e=new google.maps.InfoWindow(r);return b(e),D.current=document.createElement("div"),l&&C(google.maps.event.addListener(e,"closeclick",l)),u&&M(google.maps.event.addListener(e,"domready",u)),p&&x(google.maps.event.addListener(e,"content_changed",p)),c&&P(google.maps.event.addListener(e,"position_changed",c)),g&&O(google.maps.event.addListener(e,"zindex_changed",g)),e.setContent(D.current),i&&e.setPosition(i),a&&e.setZIndex(a),o?e.open(f,o):e.getPosition()?e.open(f):h(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),m&&m(e),()=>{L&&google.maps.event.removeListener(L),w&&google.maps.event.removeListener(w),E&&google.maps.event.removeListener(E),k&&google.maps.event.removeListener(k),S&&google.maps.event.removeListener(S),v&&v(e),e.close()}}),[]),D.current?s.createPortal(n.Children.only(t),D.current):null}));class jt extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"containerElement",null),p(this,"state",{infoWindow:null}),p(this,"open",((e,t)=>{t?e.open(this.context,t):e.getPosition()?e.open(this.context):h(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")})),p(this,"setInfoWindowCallback",(()=>{null!==this.state.infoWindow&&null!==this.containerElement&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))}))}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=b({updaterMap:Ot,eventMap:St,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({infoWindow:e})),this.setInfoWindowCallback)}componentDidUpdate(e){null!==this.state.infoWindow&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Ot,eventMap:St,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){null!==this.state.infoWindow&&(y(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?s.createPortal(n.Children.only(this.props.children),this.containerElement):null}}function It(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Bt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?It(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):It(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(jt,"contextType",d);var Tt={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},_t={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},visible(e,t){e.setVisible(t)}},Rt={};var Ut=n.memo((function(e){var{options:t,draggable:s,editable:o,visible:r,path:i,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:c,onMouseOut:h,onMouseOver:g,onMouseUp:m,onRightClick:v,onClick:f,onDrag:y,onLoad:b,onUnmount:L}=e,C=n.useContext(d),[E,M]=n.useState(null),[w,x]=n.useState(null),[k,P]=n.useState(null),[S,O]=n.useState(null),[D,j]=n.useState(null),[I,B]=n.useState(null),[T,_]=n.useState(null),[R,U]=n.useState(null),[z,A]=n.useState(null),[Z,V]=n.useState(null),[W,N]=n.useState(null),[H,F]=n.useState(null);return n.useEffect((()=>{null!==E&&E.setMap(C)}),[C]),n.useEffect((()=>{void 0!==t&&null!==E&&E.setOptions(t)}),[E,t]),n.useEffect((()=>{void 0!==s&&null!==E&&E.setDraggable(s)}),[E,s]),n.useEffect((()=>{void 0!==o&&null!==E&&E.setEditable(o)}),[E,o]),n.useEffect((()=>{void 0!==r&&null!==E&&E.setVisible(r)}),[E,r]),n.useEffect((()=>{void 0!==i&&null!==E&&E.setPath(i)}),[E,i]),n.useEffect((()=>{E&&a&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(E,"dblclick",a)))}),[a]),n.useEffect((()=>{E&&l&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(E,"dragend",l)))}),[l]),n.useEffect((()=>{E&&u&&(null!==S&&google.maps.event.removeListener(S),O(google.maps.event.addListener(E,"dragstart",u)))}),[u]),n.useEffect((()=>{E&&p&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(E,"mousedown",p)))}),[p]),n.useEffect((()=>{E&&c&&(null!==I&&google.maps.event.removeListener(I),B(google.maps.event.addListener(E,"mousemove",c)))}),[c]),n.useEffect((()=>{E&&h&&(null!==T&&google.maps.event.removeListener(T),_(google.maps.event.addListener(E,"mouseout",h)))}),[h]),n.useEffect((()=>{E&&g&&(null!==R&&google.maps.event.removeListener(R),U(google.maps.event.addListener(E,"mouseover",g)))}),[g]),n.useEffect((()=>{E&&m&&(null!==z&&google.maps.event.removeListener(z),A(google.maps.event.addListener(E,"mouseup",m)))}),[m]),n.useEffect((()=>{E&&v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(E,"rightclick",v)))}),[v]),n.useEffect((()=>{E&&f&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(E,"click",f)))}),[f]),n.useEffect((()=>{E&&y&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(E,"drag",y)))}),[y]),n.useEffect((()=>{var e=new google.maps.Polyline(Bt(Bt({},t||Rt),{},{map:C}));return i&&e.setPath(i),void 0!==r&&e.setVisible(r),void 0!==o&&e.setEditable(o),void 0!==s&&e.setDraggable(s),a&&x(google.maps.event.addListener(e,"dblclick",a)),l&&P(google.maps.event.addListener(e,"dragend",l)),u&&O(google.maps.event.addListener(e,"dragstart",u)),p&&j(google.maps.event.addListener(e,"mousedown",p)),c&&B(google.maps.event.addListener(e,"mousemove",c)),h&&_(google.maps.event.addListener(e,"mouseout",h)),g&&U(google.maps.event.addListener(e,"mouseover",g)),m&&A(google.maps.event.addListener(e,"mouseup",m)),v&&V(google.maps.event.addListener(e,"rightclick",v)),f&&N(google.maps.event.addListener(e,"click",f)),y&&F(google.maps.event.addListener(e,"drag",y)),M(e),b&&b(e),()=>{null!==w&&google.maps.event.removeListener(w),null!==k&&google.maps.event.removeListener(k),null!==S&&google.maps.event.removeListener(S),null!==D&&google.maps.event.removeListener(D),null!==I&&google.maps.event.removeListener(I),null!==T&&google.maps.event.removeListener(T),null!==R&&google.maps.event.removeListener(R),null!==z&&google.maps.event.removeListener(z),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),L&&L(e),e.setMap(null)}}),[]),null}));class zt extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{polyline:null}),p(this,"setPolylineCallback",(()=>{null!==this.state.polyline&&this.props.onLoad&&this.props.onLoad(this.state.polyline)}))}componentDidMount(){var e=new google.maps.Polyline(Bt(Bt({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:_t,eventMap:Tt,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{polyline:e}}),this.setPolylineCallback)}componentDidUpdate(e){null!==this.state.polyline&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:_t,eventMap:Tt,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){null!==this.state.polyline&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),y(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}}function At(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?At(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):At(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(zt,"contextType",d);var Vt={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},Wt={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},paths(e,t){e.setPaths(t)},visible(e,t){e.setVisible(t)}};var Nt=n.memo((function(e){var{options:t,draggable:s,editable:o,visible:r,path:i,paths:a,onDblClick:l,onDragEnd:u,onDragStart:p,onMouseDown:c,onMouseMove:h,onMouseOut:g,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onLoad:L,onUnmount:C,onEdit:E}=e,M=n.useContext(d),[w,x]=n.useState(null),[k,P]=n.useState(null),[S,O]=n.useState(null),[D,j]=n.useState(null),[I,B]=n.useState(null),[T,_]=n.useState(null),[R,U]=n.useState(null),[z,A]=n.useState(null),[Z,V]=n.useState(null),[W,N]=n.useState(null),[H,F]=n.useState(null),[G,K]=n.useState(null);return n.useEffect((()=>{null!==w&&w.setMap(M)}),[M]),n.useEffect((()=>{void 0!==t&&null!==w&&w.setOptions(t)}),[w,t]),n.useEffect((()=>{void 0!==s&&null!==w&&w.setDraggable(s)}),[w,s]),n.useEffect((()=>{void 0!==o&&null!==w&&w.setEditable(o)}),[w,o]),n.useEffect((()=>{void 0!==r&&null!==w&&w.setVisible(r)}),[w,r]),n.useEffect((()=>{void 0!==i&&null!==w&&w.setPath(i)}),[w,i]),n.useEffect((()=>{void 0!==a&&null!==w&&w.setPaths(a)}),[w,a]),n.useEffect((()=>{w&&"function"==typeof l&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(w,"dblclick",l)))}),[l]),n.useEffect((()=>{w&&(google.maps.event.addListener(w.getPath(),"insert_at",(()=>{null==E||E(w)})),google.maps.event.addListener(w.getPath(),"set_at",(()=>{null==E||E(w)})),google.maps.event.addListener(w.getPath(),"remove_at",(()=>{null==E||E(w)})))}),[w,E]),n.useEffect((()=>{w&&"function"==typeof u&&(null!==S&&google.maps.event.removeListener(S),O(google.maps.event.addListener(w,"dragend",u)))}),[u]),n.useEffect((()=>{w&&"function"==typeof p&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(w,"dragstart",p)))}),[p]),n.useEffect((()=>{w&&"function"==typeof c&&(null!==I&&google.maps.event.removeListener(I),B(google.maps.event.addListener(w,"mousedown",c)))}),[c]),n.useEffect((()=>{w&&"function"==typeof h&&(null!==T&&google.maps.event.removeListener(T),_(google.maps.event.addListener(w,"mousemove",h)))}),[h]),n.useEffect((()=>{w&&"function"==typeof g&&(null!==R&&google.maps.event.removeListener(R),U(google.maps.event.addListener(w,"mouseout",g)))}),[g]),n.useEffect((()=>{w&&"function"==typeof m&&(null!==z&&google.maps.event.removeListener(z),A(google.maps.event.addListener(w,"mouseover",m)))}),[m]),n.useEffect((()=>{w&&"function"==typeof v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(w,"mouseup",v)))}),[v]),n.useEffect((()=>{w&&"function"==typeof f&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(w,"rightclick",f)))}),[f]),n.useEffect((()=>{w&&"function"==typeof y&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(w,"click",y)))}),[y]),n.useEffect((()=>{w&&"function"==typeof b&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(w,"drag",b)))}),[b]),n.useEffect((()=>{var e=new google.maps.Polygon(Zt(Zt({},t),{},{map:M}));return i&&e.setPath(i),a&&e.setPaths(a),void 0!==r&&e.setVisible(r),void 0!==o&&e.setEditable(o),void 0!==s&&e.setDraggable(s),l&&P(google.maps.event.addListener(e,"dblclick",l)),u&&O(google.maps.event.addListener(e,"dragend",u)),p&&j(google.maps.event.addListener(e,"dragstart",p)),c&&B(google.maps.event.addListener(e,"mousedown",c)),h&&_(google.maps.event.addListener(e,"mousemove",h)),g&&U(google.maps.event.addListener(e,"mouseout",g)),m&&A(google.maps.event.addListener(e,"mouseover",m)),v&&V(google.maps.event.addListener(e,"mouseup",v)),f&&N(google.maps.event.addListener(e,"rightclick",f)),y&&F(google.maps.event.addListener(e,"click",y)),b&&K(google.maps.event.addListener(e,"drag",b)),x(e),L&&L(e),()=>{null!==k&&google.maps.event.removeListener(k),null!==S&&google.maps.event.removeListener(S),null!==D&&google.maps.event.removeListener(D),null!==I&&google.maps.event.removeListener(I),null!==T&&google.maps.event.removeListener(T),null!==R&&google.maps.event.removeListener(R),null!==z&&google.maps.event.removeListener(z),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),C&&C(e),e.setMap(null)}}),[]),null}));class Ht extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=b({updaterMap:Wt,eventMap:Vt,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Wt,eventMap:Vt,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),y(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}}function Ft(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Gt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ft(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ft(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(Ht,"contextType",d);var Kt={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},Yt={bounds(e,t){e.setBounds(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},visible(e,t){e.setVisible(t)}};var qt=n.memo((function(e){var{options:t,bounds:s,draggable:o,editable:r,visible:i,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:c,onMouseOut:h,onMouseOver:g,onMouseUp:m,onRightClick:v,onClick:f,onDrag:y,onBoundsChanged:b,onLoad:L,onUnmount:C}=e,E=n.useContext(d),[M,w]=n.useState(null),[x,k]=n.useState(null),[P,S]=n.useState(null),[O,D]=n.useState(null),[j,I]=n.useState(null),[B,T]=n.useState(null),[_,R]=n.useState(null),[U,z]=n.useState(null),[A,Z]=n.useState(null),[V,W]=n.useState(null),[N,H]=n.useState(null),[F,G]=n.useState(null),[K,Y]=n.useState(null);return n.useEffect((()=>{null!==M&&M.setMap(E)}),[E]),n.useEffect((()=>{void 0!==t&&null!==M&&M.setOptions(t)}),[M,t]),n.useEffect((()=>{void 0!==o&&null!==M&&M.setDraggable(o)}),[M,o]),n.useEffect((()=>{void 0!==r&&null!==M&&M.setEditable(r)}),[M,r]),n.useEffect((()=>{void 0!==i&&null!==M&&M.setVisible(i)}),[M,i]),n.useEffect((()=>{void 0!==s&&null!==M&&M.setBounds(s)}),[M,s]),n.useEffect((()=>{M&&a&&(null!==x&&google.maps.event.removeListener(x),k(google.maps.event.addListener(M,"dblclick",a)))}),[a]),n.useEffect((()=>{M&&l&&(null!==P&&google.maps.event.removeListener(P),S(google.maps.event.addListener(M,"dragend",l)))}),[l]),n.useEffect((()=>{M&&u&&(null!==O&&google.maps.event.removeListener(O),D(google.maps.event.addListener(M,"dragstart",u)))}),[u]),n.useEffect((()=>{M&&p&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(M,"mousedown",p)))}),[p]),n.useEffect((()=>{M&&c&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(M,"mousemove",c)))}),[c]),n.useEffect((()=>{M&&h&&(null!==_&&google.maps.event.removeListener(_),R(google.maps.event.addListener(M,"mouseout",h)))}),[h]),n.useEffect((()=>{M&&g&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(M,"mouseover",g)))}),[g]),n.useEffect((()=>{M&&m&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(M,"mouseup",m)))}),[m]),n.useEffect((()=>{M&&v&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(M,"rightclick",v)))}),[v]),n.useEffect((()=>{M&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(M,"click",f)))}),[f]),n.useEffect((()=>{M&&y&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(M,"drag",y)))}),[y]),n.useEffect((()=>{M&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(M,"bounds_changed",b)))}),[b]),n.useEffect((()=>{var e=new google.maps.Rectangle(Gt(Gt({},t),{},{map:E}));return void 0!==i&&e.setVisible(i),void 0!==r&&e.setEditable(r),void 0!==o&&e.setDraggable(o),void 0!==s&&e.setBounds(s),a&&k(google.maps.event.addListener(e,"dblclick",a)),l&&S(google.maps.event.addListener(e,"dragend",l)),u&&D(google.maps.event.addListener(e,"dragstart",u)),p&&I(google.maps.event.addListener(e,"mousedown",p)),c&&T(google.maps.event.addListener(e,"mousemove",c)),h&&R(google.maps.event.addListener(e,"mouseout",h)),g&&z(google.maps.event.addListener(e,"mouseover",g)),m&&Z(google.maps.event.addListener(e,"mouseup",m)),v&&W(google.maps.event.addListener(e,"rightclick",v)),f&&H(google.maps.event.addListener(e,"click",f)),y&&G(google.maps.event.addListener(e,"drag",y)),b&&Y(google.maps.event.addListener(e,"bounds_changed",b)),w(e),L&&L(e),()=>{null!==x&&google.maps.event.removeListener(x),null!==P&&google.maps.event.removeListener(P),null!==O&&google.maps.event.removeListener(O),null!==j&&google.maps.event.removeListener(j),null!==B&&google.maps.event.removeListener(B),null!==_&&google.maps.event.removeListener(_),null!==U&&google.maps.event.removeListener(U),null!==A&&google.maps.event.removeListener(A),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==F&&google.maps.event.removeListener(F),null!==K&&google.maps.event.removeListener(K),C&&C(e),e.setMap(null)}}),[]),null}));class Jt extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{rectangle:null}),p(this,"setRectangleCallback",(()=>{null!==this.state.rectangle&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)}))}componentDidMount(){var e=new google.maps.Rectangle(Gt(Gt({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:Yt,eventMap:Kt,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{rectangle:e}}),this.setRectangleCallback)}componentDidUpdate(e){null!==this.state.rectangle&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Yt,eventMap:Kt,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){null!==this.state.rectangle&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),y(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function $t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(Jt,"contextType",d);var Qt={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},en={center(e,t){e.setCenter(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},radius(e,t){e.setRadius(t)},visible(e,t){e.setVisible(t)}},tn={};var nn=n.memo((function(e){var{options:t,center:s,radius:o,draggable:r,editable:i,visible:a,onDblClick:l,onDragEnd:u,onDragStart:p,onMouseDown:c,onMouseMove:h,onMouseOut:g,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onCenterChanged:L,onRadiusChanged:C,onLoad:E,onUnmount:M}=e,w=n.useContext(d),[x,k]=n.useState(null),[P,S]=n.useState(null),[O,D]=n.useState(null),[j,I]=n.useState(null),[B,T]=n.useState(null),[_,R]=n.useState(null),[U,z]=n.useState(null),[A,Z]=n.useState(null),[V,W]=n.useState(null),[N,H]=n.useState(null),[F,G]=n.useState(null),[K,Y]=n.useState(null),[q,J]=n.useState(null),[X,$]=n.useState(null);return n.useEffect((()=>{null!==x&&x.setMap(w)}),[w]),n.useEffect((()=>{void 0!==t&&null!==x&&x.setOptions(t)}),[x,t]),n.useEffect((()=>{void 0!==r&&null!==x&&x.setDraggable(r)}),[x,r]),n.useEffect((()=>{void 0!==i&&null!==x&&x.setEditable(i)}),[x,i]),n.useEffect((()=>{void 0!==a&&null!==x&&x.setVisible(a)}),[x,a]),n.useEffect((()=>{"number"==typeof o&&null!==x&&x.setRadius(o)}),[x,o]),n.useEffect((()=>{void 0!==s&&null!==x&&x.setCenter(s)}),[x,s]),n.useEffect((()=>{x&&l&&(null!==P&&google.maps.event.removeListener(P),S(google.maps.event.addListener(x,"dblclick",l)))}),[l]),n.useEffect((()=>{x&&u&&(null!==O&&google.maps.event.removeListener(O),D(google.maps.event.addListener(x,"dragend",u)))}),[u]),n.useEffect((()=>{x&&p&&(null!==j&&google.maps.event.removeListener(j),I(google.maps.event.addListener(x,"dragstart",p)))}),[p]),n.useEffect((()=>{x&&c&&(null!==B&&google.maps.event.removeListener(B),T(google.maps.event.addListener(x,"mousedown",c)))}),[c]),n.useEffect((()=>{x&&h&&(null!==_&&google.maps.event.removeListener(_),R(google.maps.event.addListener(x,"mousemove",h)))}),[h]),n.useEffect((()=>{x&&g&&(null!==U&&google.maps.event.removeListener(U),z(google.maps.event.addListener(x,"mouseout",g)))}),[g]),n.useEffect((()=>{x&&m&&(null!==A&&google.maps.event.removeListener(A),Z(google.maps.event.addListener(x,"mouseover",m)))}),[m]),n.useEffect((()=>{x&&v&&(null!==V&&google.maps.event.removeListener(V),W(google.maps.event.addListener(x,"mouseup",v)))}),[v]),n.useEffect((()=>{x&&f&&(null!==N&&google.maps.event.removeListener(N),H(google.maps.event.addListener(x,"rightclick",f)))}),[f]),n.useEffect((()=>{x&&y&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(x,"click",y)))}),[y]),n.useEffect((()=>{x&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(x,"drag",b)))}),[b]),n.useEffect((()=>{x&&L&&(null!==q&&google.maps.event.removeListener(q),J(google.maps.event.addListener(x,"center_changed",L)))}),[y]),n.useEffect((()=>{x&&C&&(null!==X&&google.maps.event.removeListener(X),$(google.maps.event.addListener(x,"radius_changed",C)))}),[C]),n.useEffect((()=>{var e=new google.maps.Circle($t($t({},t||tn),{},{map:w}));return"number"==typeof o&&e.setRadius(o),void 0!==s&&e.setCenter(s),"number"==typeof o&&e.setRadius(o),void 0!==a&&e.setVisible(a),void 0!==i&&e.setEditable(i),void 0!==r&&e.setDraggable(r),l&&S(google.maps.event.addListener(e,"dblclick",l)),u&&D(google.maps.event.addListener(e,"dragend",u)),p&&I(google.maps.event.addListener(e,"dragstart",p)),c&&T(google.maps.event.addListener(e,"mousedown",c)),h&&R(google.maps.event.addListener(e,"mousemove",h)),g&&z(google.maps.event.addListener(e,"mouseout",g)),m&&Z(google.maps.event.addListener(e,"mouseover",m)),v&&W(google.maps.event.addListener(e,"mouseup",v)),f&&H(google.maps.event.addListener(e,"rightclick",f)),y&&G(google.maps.event.addListener(e,"click",y)),b&&Y(google.maps.event.addListener(e,"drag",b)),L&&J(google.maps.event.addListener(e,"center_changed",L)),C&&$(google.maps.event.addListener(e,"radius_changed",C)),k(e),E&&E(e),()=>{null!==P&&google.maps.event.removeListener(P),null!==O&&google.maps.event.removeListener(O),null!==j&&google.maps.event.removeListener(j),null!==B&&google.maps.event.removeListener(B),null!==_&&google.maps.event.removeListener(_),null!==U&&google.maps.event.removeListener(U),null!==A&&google.maps.event.removeListener(A),null!==V&&google.maps.event.removeListener(V),null!==N&&google.maps.event.removeListener(N),null!==F&&google.maps.event.removeListener(F),null!==q&&google.maps.event.removeListener(q),null!==X&&google.maps.event.removeListener(X),M&&M(e),e.setMap(null)}}),[]),null}));class sn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{circle:null}),p(this,"setCircleCallback",(()=>{null!==this.state.circle&&this.props.onLoad&&this.props.onLoad(this.state.circle)}))}componentDidMount(){var e=new google.maps.Circle($t($t({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:en,eventMap:Qt,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{circle:e}}),this.setCircleCallback)}componentDidUpdate(e){null!==this.state.circle&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:en,eventMap:Qt,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){var e;null!==this.state.circle&&(this.props.onUnmount&&this.props.onUnmount(this.state.circle),y(this.registeredEvents),null===(e=this.state.circle)||void 0===e||e.setMap(null))}render(){return null}}function on(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function rn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?on(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):on(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(sn,"contextType",d);var an={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},ln={add(e,t){e.add(t)},addgeojson(e,t,n){e.addGeoJson(t,n)},contains(e,t){e.contains(t)},foreach(e,t){e.forEach(t)},loadgeojson(e,t,n,s){e.loadGeoJson(t,n,s)},overridestyle(e,t,n){e.overrideStyle(t,n)},remove(e,t){e.remove(t)},revertstyle(e,t){e.revertStyle(t)},controlposition(e,t){e.setControlPosition(t)},controls(e,t){e.setControls(t)},drawingmode(e,t){e.setDrawingMode(t)},map(e,t){e.setMap(t)},style(e,t){e.setStyle(t)},togeojson(e,t){e.toGeoJson(t)}};var un=n.memo((function(e){var{options:t,onClick:s,onDblClick:o,onMouseDown:r,onMouseMove:i,onMouseOut:a,onMouseOver:l,onMouseUp:u,onRightClick:p,onAddFeature:c,onRemoveFeature:h,onRemoveProperty:g,onSetGeometry:m,onSetProperty:v,onLoad:f,onUnmount:y}=e,b=n.useContext(d),[L,C]=n.useState(null),[E,M]=n.useState(null),[w,x]=n.useState(null),[k,P]=n.useState(null),[S,O]=n.useState(null),[D,j]=n.useState(null),[I,B]=n.useState(null),[T,_]=n.useState(null),[R,U]=n.useState(null),[z,A]=n.useState(null),[Z,V]=n.useState(null),[W,N]=n.useState(null),[H,F]=n.useState(null),[G,K]=n.useState(null);return n.useEffect((()=>{null!==L&&L.setMap(b)}),[b]),n.useEffect((()=>{L&&o&&(null!==E&&google.maps.event.removeListener(E),M(google.maps.event.addListener(L,"dblclick",o)))}),[o]),n.useEffect((()=>{L&&r&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(L,"mousedown",r)))}),[r]),n.useEffect((()=>{L&&i&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(L,"mousemove",i)))}),[i]),n.useEffect((()=>{L&&a&&(null!==S&&google.maps.event.removeListener(S),O(google.maps.event.addListener(L,"mouseout",a)))}),[a]),n.useEffect((()=>{L&&l&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(L,"mouseover",l)))}),[l]),n.useEffect((()=>{L&&u&&(null!==I&&google.maps.event.removeListener(I),B(google.maps.event.addListener(L,"mouseup",u)))}),[u]),n.useEffect((()=>{L&&p&&(null!==T&&google.maps.event.removeListener(T),_(google.maps.event.addListener(L,"rightclick",p)))}),[p]),n.useEffect((()=>{L&&s&&(null!==R&&google.maps.event.removeListener(R),U(google.maps.event.addListener(L,"click",s)))}),[s]),n.useEffect((()=>{L&&c&&(null!==z&&google.maps.event.removeListener(z),A(google.maps.event.addListener(L,"addfeature",c)))}),[c]),n.useEffect((()=>{L&&h&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(L,"removefeature",h)))}),[h]),n.useEffect((()=>{L&&g&&(null!==W&&google.maps.event.removeListener(W),N(google.maps.event.addListener(L,"removeproperty",g)))}),[g]),n.useEffect((()=>{L&&m&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(L,"setgeometry",m)))}),[m]),n.useEffect((()=>{L&&v&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(L,"setproperty",v)))}),[v]),n.useEffect((()=>{if(null!==b){var e=new google.maps.Data(rn(rn({},t),{},{map:b}));o&&M(google.maps.event.addListener(e,"dblclick",o)),r&&x(google.maps.event.addListener(e,"mousedown",r)),i&&P(google.maps.event.addListener(e,"mousemove",i)),a&&O(google.maps.event.addListener(e,"mouseout",a)),l&&j(google.maps.event.addListener(e,"mouseover",l)),u&&B(google.maps.event.addListener(e,"mouseup",u)),p&&_(google.maps.event.addListener(e,"rightclick",p)),s&&U(google.maps.event.addListener(e,"click",s)),c&&A(google.maps.event.addListener(e,"addfeature",c)),h&&V(google.maps.event.addListener(e,"removefeature",h)),g&&N(google.maps.event.addListener(e,"removeproperty",g)),m&&F(google.maps.event.addListener(e,"setgeometry",m)),v&&K(google.maps.event.addListener(e,"setproperty",v)),C(e),f&&f(e)}return()=>{L&&(null!==E&&google.maps.event.removeListener(E),null!==w&&google.maps.event.removeListener(w),null!==k&&google.maps.event.removeListener(k),null!==S&&google.maps.event.removeListener(S),null!==D&&google.maps.event.removeListener(D),null!==I&&google.maps.event.removeListener(I),null!==T&&google.maps.event.removeListener(T),null!==R&&google.maps.event.removeListener(R),null!==z&&google.maps.event.removeListener(z),null!==Z&&google.maps.event.removeListener(Z),null!==W&&google.maps.event.removeListener(W),null!==H&&google.maps.event.removeListener(H),null!==G&&google.maps.event.removeListener(G),y&&y(L),L.setMap(null))}}),[]),null}));class pn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{data:null}),p(this,"setDataCallback",(()=>{null!==this.state.data&&this.props.onLoad&&this.props.onLoad(this.state.data)}))}componentDidMount(){if(null!==this.context){var e=new google.maps.Data(rn(rn({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:ln,eventMap:an,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({data:e})),this.setDataCallback)}}componentDidUpdate(e){null!==this.state.data&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:ln,eventMap:an,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){null!==this.state.data&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),y(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}}function cn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function hn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cn(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(pn,"contextType",d);var dn={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},gn={options(e,t){e.setOptions(t)},url(e,t){e.setUrl(t)},zIndex(e,t){e.setZIndex(t)}};class mn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{kmlLayer:null}),p(this,"setKmlLayerCallback",(()=>{null!==this.state.kmlLayer&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)}))}componentDidMount(){var e=new google.maps.KmlLayer(hn(hn({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:gn,eventMap:dn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{kmlLayer:e}}),this.setKmlLayerCallback)}componentDidUpdate(e){null!==this.state.kmlLayer&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:gn,eventMap:dn,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){null!==this.state.kmlLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),y(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}}function vn(e,t){return"function"==typeof t?t(e.offsetWidth,e.offsetHeight):{x:0,y:0}}function fn(e,t){return new t(e.lat,e.lng)}function yn(e,t){return new t(new google.maps.LatLng(e.ne.lat,e.ne.lng),new google.maps.LatLng(e.sw.lat,e.sw.lng))}function bn(e,t,n,s){return void 0!==n?function(e,t,n){var s=e&&e.fromLatLngToDivPixel(n.getNorthEast()),o=e&&e.fromLatLngToDivPixel(n.getSouthWest());return s&&o?{left:"".concat(o.x+t.x,"px"),top:"".concat(s.y+t.y,"px"),width:"".concat(s.x-o.x-t.x,"px"),height:"".concat(o.y-s.y-t.y,"px")}:{left:"-9999px",top:"-9999px"}}(e,t,(o=n,r=google.maps.LatLngBounds,i=yn,o instanceof r?o:i(o,r))):function(e,t,n){var s=e&&e.fromLatLngToDivPixel(n);if(s){var{x:o,y:r}=s;return{left:"".concat(o+t.x,"px"),top:"".concat(r+t.y,"px")}}return{left:"-9999px",top:"-9999px"}}(e,t,function(e,t,n){return e instanceof t?e:n(e,t)}(s,google.maps.LatLng,fn));var o,r,i}function Ln(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Cn(e,t,n,s,o){class r extends google.maps.OverlayView{constructor(e,t,n,s){super(),this.container=e,this.pane=t,this.position=n,this.bounds=s}onAdd(){var e,t=null===(e=this.getPanes())||void 0===e?void 0:e[this.pane];null==t||t.appendChild(this.container)}draw(){var e=this.getProjection(),t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ln(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ln(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},this.container?vn(this.container,o):{x:0,y:0}),n=bn(e,t,this.bounds,this.position);for(var[s,r]of Object.entries(n))this.container.style[s]=r}onRemove(){null!==this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}return new r(e,t,n,s)}function En(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Mn(e){return e?(e instanceof google.maps.LatLng?e:new google.maps.LatLng(e.lat,e.lng))+"":""}function wn(e){return e?(e instanceof google.maps.LatLngBounds?e:new google.maps.LatLngBounds(new google.maps.LatLng(e.south,e.east),new google.maps.LatLng(e.north,e.west)))+"":""}p(mn,"contextType",d);var xn=n.memo((function(e){var{position:t,bounds:s,mapPaneName:o,zIndex:r,onLoad:i,onUnmount:l,getPixelPositionOffset:u,children:p}=e,c=n.useContext(d),h=n.useMemo((()=>{var e=document.createElement("div");return e.style.position="absolute",e}),[]),g=n.useMemo((()=>Cn(h,o,t,s,u)),[h,o,t,s]);return n.useEffect((()=>(null==i||i(g),null==g||g.setMap(c),()=>{null==l||l(g),null==g||g.setMap(null)})),[c,g]),n.useEffect((()=>{h.style.zIndex="".concat(r)}),[r,h]),a.createPortal(p,h)}));class kn extends n.PureComponent{constructor(e){super(e),p(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),p(this,"updatePane",(()=>{var e=this.props.mapPaneName,t=this.overlayView.getPanes();h(!!e,"OverlayView requires props.mapPaneName but got %s",e),t?this.setState({paneEl:t[e]}):this.setState({paneEl:null})})),p(this,"onAdd",(()=>{var e,t;this.updatePane(),null===(e=(t=this.props).onLoad)||void 0===e||e.call(t,this.overlayView)})),p(this,"onPositionElement",(()=>{var e,t,n,s,o,r,i=this.overlayView.getProjection(),a=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?En(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):En(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({x:0,y:0},this.containerRef.current?vn(this.containerRef.current,this.props.getPixelPositionOffset):{}),l=bn(i,a,this.props.bounds,this.props.position);(o=l,r={left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height},o.left!==r.left||o.top!==r.top||o.width!==r.height||o.height!==r.height)&&this.setState({containerStyle:{top:null!==(e=l.top)&&void 0!==e?e:0,left:null!==(t=l.left)&&void 0!==t?t:0,width:null!==(n=l.width)&&void 0!==n?n:0,height:null!==(s=l.height)&&void 0!==s?s:0,position:"absolute"}})})),p(this,"draw",(()=>{this.onPositionElement()})),p(this,"onRemove",(()=>{var e,t;this.setState((()=>({paneEl:null}))),null===(e=(t=this.props).onUnmount)||void 0===e||e.call(t,this.overlayView)})),this.containerRef=n.createRef();var t=new google.maps.OverlayView;t.onAdd=this.onAdd,t.draw=this.draw,t.onRemove=this.onRemove,this.overlayView=t}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var t=Mn(e.position),n=Mn(this.props.position),s=wn(e.bounds),o=wn(this.props.bounds);t===n&&s===o||this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var e=this.state.paneEl;return e?a.createPortal(t.jsx("div",{ref:this.containerRef,style:this.state.containerStyle,children:n.Children.only(this.props.children)}),e):null}}function Pn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Sn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pn(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(kn,"FLOAT_PANE","floatPane"),p(kn,"MAP_PANE","mapPane"),p(kn,"MARKER_LAYER","markerLayer"),p(kn,"OVERLAY_LAYER","overlayLayer"),p(kn,"OVERLAY_MOUSE_TARGET","overlayMouseTarget"),p(kn,"contextType",d);var On={onDblClick:"dblclick",onClick:"click"},Dn={opacity(e,t){e.setOpacity(t)}};var jn=n.memo((function(e){var{url:t,bounds:s,options:o,visible:r}=e,i=n.useContext(d),a=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east)),l=n.useMemo((()=>new google.maps.GroundOverlay(t,a,o)),[]);return n.useEffect((()=>{null!==l&&l.setMap(i)}),[i]),n.useEffect((()=>{void 0!==t&&null!==l&&(l.set("url",t),l.setMap(i))}),[l,t]),n.useEffect((()=>{void 0!==r&&null!==l&&l.setOpacity(r?1:0)}),[l,r]),n.useEffect((()=>{var e=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east));void 0!==s&&null!==l&&(l.set("bounds",e),l.setMap(i))}),[l,s]),null}));class In extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{groundOverlay:null}),p(this,"setGroundOverlayCallback",(()=>{null!==this.state.groundOverlay&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)}))}componentDidMount(){h(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,Sn(Sn({},this.props.options),{},{map:this.context}));this.registeredEvents=b({updaterMap:Dn,eventMap:On,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{groundOverlay:e}}),this.setGroundOverlayCallback)}componentDidUpdate(e){null!==this.state.groundOverlay&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Dn,eventMap:On,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}}function Bn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Tn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bn(Object(n),!0).forEach((function(t){p(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}p(In,"defaultProps",{onLoad:function(){}}),p(In,"contextType",d);var _n={},Rn={data(e,t){e.setData(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)}};var Un=n.memo((function(e){var{data:t,onLoad:s,onUnmount:o,options:r}=e,i=n.useContext(d),[a,l]=n.useState(null);return n.useEffect((()=>{google.maps.visualization||h(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)}),[]),n.useEffect((()=>{h(!!t,"data property is required in HeatmapLayer %s",t)}),[t]),n.useEffect((()=>{null!==a&&a.setMap(i)}),[i]),n.useEffect((()=>{r&&null!==a&&a.setOptions(r)}),[a,r]),n.useEffect((()=>{var e=new google.maps.visualization.HeatmapLayer(Tn(Tn({},r),{},{data:t,map:i}));return l(e),s&&s(e),()=>{null!==a&&(o&&o(a),a.setMap(null))}}),[]),null}));class zn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{heatmapLayer:null}),p(this,"setHeatmapLayerCallback",(()=>{null!==this.state.heatmapLayer&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)}))}componentDidMount(){h(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),h(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(Tn(Tn({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=b({updaterMap:Rn,eventMap:_n,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{heatmapLayer:e}}),this.setHeatmapLayerCallback)}componentDidUpdate(e){y(this.registeredEvents),this.registeredEvents=b({updaterMap:Rn,eventMap:_n,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){null!==this.state.heatmapLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),y(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}}p(zn,"contextType",d);var An={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},Zn={register(e,t,n){e.registerPanoProvider(t,n)},links(e,t){e.setLinks(t)},motionTracking(e,t){e.setMotionTracking(t)},options(e,t){e.setOptions(t)},pano(e,t){e.setPano(t)},position(e,t){e.setPosition(t)},pov(e,t){e.setPov(t)},visible(e,t){e.setVisible(t)},zoom(e,t){e.setZoom(t)}};class Vn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{streetViewPanorama:null}),p(this,"setStreetViewPanoramaCallback",(()=>{null!==this.state.streetViewPanorama&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)}))}componentDidMount(){var e,t,n=null!==(e=null===(t=this.context)||void 0===t?void 0:t.getStreetView())&&void 0!==e?e:null;this.registeredEvents=b({updaterMap:Zn,eventMap:An,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({streetViewPanorama:n})),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){null!==this.state.streetViewPanorama&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Zn,eventMap:An,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){null!==this.state.streetViewPanorama&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),y(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}}p(Vn,"contextType",d);class Wn extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{streetViewService:null}),p(this,"setStreetViewServiceCallback",(()=>{null!==this.state.streetViewService&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)}))}componentDidMount(){var e=new google.maps.StreetViewService;this.setState((function(){return{streetViewService:e}}),this.setStreetViewServiceCallback)}componentWillUnmount(){null!==this.state.streetViewService&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}}p(Wn,"contextType",d);class Nn extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{directionsService:null}),p(this,"setDirectionsServiceCallback",(()=>{null!==this.state.directionsService&&this.props.onLoad&&this.props.onLoad(this.state.directionsService)}))}componentDidMount(){h(!!this.props.options,"DirectionsService expected options object as parameter, but got %s",this.props.options);var e=new google.maps.DirectionsService;this.setState((function(){return{directionsService:e}}),this.setDirectionsServiceCallback)}componentDidUpdate(){null!==this.state.directionsService&&this.state.directionsService.route(this.props.options,this.props.callback)}componentWillUnmount(){null!==this.state.directionsService&&this.props.onUnmount&&this.props.onUnmount(this.state.directionsService)}render(){return null}}var Hn={onDirectionsChanged:"directions_changed"},Fn={directions(e,t){e.setDirections(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},panel(e,t){e.setPanel(t)},routeIndex(e,t){e.setRouteIndex(t)}};class Gn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"state",{directionsRenderer:null}),p(this,"setDirectionsRendererCallback",(()=>{null!==this.state.directionsRenderer&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))}))}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=b({updaterMap:Fn,eventMap:Hn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{directionsRenderer:e}}),this.setDirectionsRendererCallback)}componentDidUpdate(e){null!==this.state.directionsRenderer&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:Fn,eventMap:Hn,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){null!==this.state.directionsRenderer&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),y(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}}p(Gn,"contextType",d);class Kn extends n.PureComponent{constructor(){super(...arguments),p(this,"state",{distanceMatrixService:null}),p(this,"setDistanceMatrixServiceCallback",(()=>{null!==this.state.distanceMatrixService&&this.props.onLoad&&this.props.onLoad(this.state.distanceMatrixService)}))}componentDidMount(){h(!!this.props.options,"DistanceMatrixService expected options object as parameter, but go %s",this.props.options);var e=new google.maps.DistanceMatrixService;this.setState((function(){return{distanceMatrixService:e}}),this.setDistanceMatrixServiceCallback)}componentDidUpdate(){null!==this.state.distanceMatrixService&&this.state.distanceMatrixService.getDistanceMatrix(this.props.options,this.props.callback)}componentWillUnmount(){null!==this.state.distanceMatrixService&&this.props.onUnmount&&this.props.onUnmount(this.state.distanceMatrixService)}render(){return null}}var Yn={onPlacesChanged:"places_changed"},qn={bounds(e,t){e.setBounds(t)}};class Jn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"containerElement",n.createRef()),p(this,"state",{searchBox:null}),p(this,"setSearchBoxCallback",(()=>{null!==this.state.searchBox&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)}))}componentDidMount(){if(h(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),null!==this.containerElement&&null!==this.containerElement.current){var e=this.containerElement.current.querySelector("input");if(null!==e){var t=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=b({updaterMap:qn,eventMap:Yn,prevProps:{},nextProps:this.props,instance:t}),this.setState((function(){return{searchBox:t}}),this.setSearchBoxCallback)}}}componentDidUpdate(e){null!==this.state.searchBox&&(y(this.registeredEvents),this.registeredEvents=b({updaterMap:qn,eventMap:Yn,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){null!==this.state.searchBox&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),y(this.registeredEvents))}render(){return t.jsx("div",{ref:this.containerElement,children:n.Children.only(this.props.children)})}}p(Jn,"contextType",d);var Xn={onPlaceChanged:"place_changed"},$n={bounds(e,t){e.setBounds(t)},restrictions(e,t){e.setComponentRestrictions(t)},fields(e,t){e.setFields(t)},options(e,t){e.setOptions(t)},types(e,t){e.setTypes(t)}};class Qn extends n.PureComponent{constructor(){super(...arguments),p(this,"registeredEvents",[]),p(this,"containerElement",n.createRef()),p(this,"state",{autocomplete:null}),p(this,"setAutocompleteCallback",(()=>{null!==this.state.autocomplete&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)}))}componentDidMount(){var e;h(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var t=null===(e=this.containerElement.current)||void 0===e?void 0:e.querySelector("input");if(t){var n=new google.maps.places.Autocomplete(t,this.props.options);this.registeredEvents=b({updaterMap:$n,eventMap:Xn,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({autocomplete:n})),this.setAutocompleteCallback)}}componentDidUpdate(e){y(this.registeredEvents),this.registeredEvents=b({updaterMap:$n,eventMap:Xn,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){null!==this.state.autocomplete&&y(this.registeredEvents)}render(){return t.jsx("div",{ref:this.containerElement,className:this.props.className,children:n.Children.only(this.props.children)})}}p(Qn,"defaultProps",{className:""}),p(Qn,"contextType",d),e.Autocomplete=Qn,e.BicyclingLayer=te,e.BicyclingLayerF=ee,e.Circle=sn,e.CircleF=nn,e.Data=pn,e.DataF=un,e.DirectionsRenderer=Gn,e.DirectionsService=Nn,e.DistanceMatrixService=Kn,e.DrawingManager=ue,e.DrawingManagerF=le,e.FLOAT_PANE="floatPane",e.GoogleMap=E,e.GoogleMapsMarkerClusterer=wt,e.GoogleMarkerClusterer=Pt,e.GroundOverlay=In,e.GroundOverlayF=jn,e.HeatmapLayer=zn,e.HeatmapLayerF=Un,e.InfoBox=Ze,e.InfoBoxF=Ae,e.InfoWindow=jt,e.InfoWindowF=Dt,e.KmlLayer=mn,e.LoadScript=T,e.LoadScriptNext=A,e.MAP_PANE="mapPane",e.MARKER_LAYER="markerLayer",e.MapContext=d,e.Marker=ve,e.MarkerClusterer=Pe,e.MarkerClustererF=ke,e.MarkerF=me,e.OVERLAY_LAYER="overlayLayer",e.OVERLAY_MOUSE_TARGET="overlayMouseTarget",e.OverlayView=kn,e.OverlayViewF=xn,e.Polygon=Ht,e.PolygonF=Nt,e.Polyline=zt,e.PolylineF=Ut,e.Rectangle=Jt,e.RectangleF=qt,e.StandaloneSearchBox=Jn,e.StreetViewPanorama=Vn,e.StreetViewService=Wn,e.TrafficLayer=Q,e.TrafficLayerF=$,e.TransitLayer=se,e.TransitLayerF=ne,e.useGoogleMap=g,e.useJsApiLoader=function(e){var{id:t=B.id,version:s=B.version,nonce:o,googleMapsApiKey:r,language:i,region:a,libraries:l=K,preventGoogleFontsLoading:u,mapIds:p,authReferrerPolicy:c}=e,h=n.useRef(!1),[d,g]=n.useState(!1),[m,v]=n.useState(void 0);n.useEffect((function(){return h.current=!0,()=>{h.current=!1}}),[]);var f=n.useMemo((()=>new G({id:t,apiKey:r,version:s,libraries:l,language:i||"en",region:a||"US",mapIds:p||[],nonce:o||"",authReferrerPolicy:c||"origin"})),[t,r,s,l,i,a,p,o,c]);n.useEffect((function(){d||f.load().then((()=>{h.current&&g(!0)})).catch((e=>{v(e)}))}),[]),n.useEffect((()=>{k&&u&&O()}),[u]);var y=n.useRef();return n.useEffect((()=>{y.current&&l!==y.current&&console.warn("Performance warning! LoadScript has been reloaded unintentionally! You should not pass `libraries` prop as new array. Please keep an array of libraries as static class property for Components and PureComponents, or just a const variable outside of component, or somewhere in config files or ENV variables"),y.current=l}),[l]),{isLoaded:d,loadError:m}},e.useLoadScript=R}));
//# sourceMappingURL=umd.min.js.map
